'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Loader2, ArrowLeft } from 'lucide-react';
import { doc, getDoc } from 'firebase/firestore';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { PartnerForm } from '../../_components/partner-form';
import { db } from '@/lib/firebase';
import { Partner } from '@/types/partner';

export default function EditPartnerPage() {
  const params = useParams<{ id: string }>();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [partner, setPartner] = useState<Partner | null>(null);
  
  const id = params?.id;
  
  if (!id) {
    return <div>Partner ID is required</div>;
  }

  useEffect(() => {
    async function fetchPartner() {
      if (!id) {
        toast({
          title: 'Error',
          description: 'Invalid partner ID',
          variant: 'destructive',
        });
        router.push('/dashboard/partners');
        return;
      }
      
      try {
        const partnerId = params.id as string;
        const docRef = doc(db, 'partners', partnerId);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          const data = docSnap.data();
          setPartner({
            id: docSnap.id,
            name: data.name || '',
            description: data.description || '',
            logoUrl: data.logoUrl || '',
            websiteUrl: data.websiteUrl || '',
            contactEmail: data.contactEmail || '',
            contactPhone: data.contactPhone || '',
            address: data.address || '',
            type: data.type || 'partner',
            featured: data.featured || false,
            status: data.status || 'draft',
            createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
            updatedAt: data.updatedAt?.toDate?.()?.toISOString() || new Date().toISOString(),
          });
        } else {
          toast({
            title: 'Error',
            description: 'Partner not found',
            variant: 'destructive',
          });
          router.push('/dashboard/partners');
        }
      } catch (error) {
        console.error('Error fetching partner:', error);
        toast({
          title: 'Error',
          description: 'Failed to load partner data',
          variant: 'destructive',
        });
        router.push('/dashboard/partners');
      } finally {
        setLoading(false);
      }
    }

    fetchPartner();
  }, [params.id, router, toast]);

  const handleSuccess = () => {
    toast({
      title: 'Success',
      description: 'Partner updated successfully',
    });
    router.push('/dashboard/partners');
  };

  const handleCancel = () => {
    router.back();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!partner) {
    return (
      <div className="container mx-auto py-6">
        <p>Partner not found</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="icon"
            onClick={handleCancel}
            disabled={loading}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Edit Partner</h1>
            <p className="text-sm text-muted-foreground">
              Update the partner details below
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Partner Information</CardTitle>
        </CardHeader>
        <CardContent>
          <PartnerForm partner={partner} onSuccess={handleSuccess} onCancel={handleCancel} />
        </CardContent>
      </Card>
    </div>
  );
}
