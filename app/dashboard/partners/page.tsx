'use client';

import { useReducer, useEffect, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { Plus, Loader2, RefreshCw } from 'lucide-react';
import { collection, getDocs, deleteDoc, doc, updateDoc, serverTimestamp, query, orderBy } from 'firebase/firestore';
import { ref, getDownloadURL } from 'firebase/storage';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { PartnersTable } from './_components/partners-table-fixed';
import { Partner } from '@/types/partner';
import { db, storage } from '@/lib/firebase';
import { useSyncData } from '@/hooks/useSyncData';

type State = {
  partners: Partner[];
  loading: boolean;
  error: string | null;
  updatingId: string | null;
  deletingId: string | null;
  showDeleteDialog: boolean;
  itemToDelete: string | null;
};

type Action =
  | { type: 'SET_PARTNERS'; payload: Partner[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_UPDATING_ID'; payload: string | null }
  | { type: 'SET_DELETING_ID'; payload: string | null }
  | { type: 'SHOW_DELETE_DIALOG'; payload: boolean }
  | { type: 'SET_ITEM_TO_DELETE'; payload: string | null }
  | { type: 'UPDATE_PARTNER'; payload: { id: string; updates: Partial<Partner> } }
  | { type: 'DELETE_PARTNER'; payload: string };

const initialState: State = {
  partners: [],
  loading: true,
  error: null,
  updatingId: null,
  deletingId: null,
  showDeleteDialog: false,
  itemToDelete: null,
};

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case 'SET_PARTNERS':
      return { ...state, partners: action.payload };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_UPDATING_ID':
      return { ...state, updatingId: action.payload };
    case 'SET_DELETING_ID':
      return { ...state, deletingId: action.payload };
    case 'SHOW_DELETE_DIALOG':
      return { ...state, showDeleteDialog: action.payload };
    case 'SET_ITEM_TO_DELETE':
      return { ...state, itemToDelete: action.payload };
    case 'UPDATE_PARTNER':
      return {
        ...state,
        partners: state.partners.map(partner =>
          partner.id === action.payload.id ? { ...partner, ...action.payload.updates } : partner
        ),
      };
    case 'DELETE_PARTNER':
      return {
        ...state,
        partners: state.partners.filter(partner => partner.id !== action.payload),
      };
    default:
      return state;
  }
}

export default function PartnersPage() {
  const { toast } = useToast();
  const [state, dispatch] = useReducer(reducer, initialState);
  
  const {
    partners,
    loading,
    error,
    updatingId,
    deletingId,
    showDeleteDialog,
    itemToDelete,
  } = state;

  const fetchPartners = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });
    
    try {
      console.log('[PartnersPage] Fetching partners from Firestore...');
      const q = query(collection(db, 'partners'), orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);
      
      const items: Partner[] = [];
      
      for (const doc of snapshot.docs) {
        const data = doc.data();
        items.push({
          id: doc.id,
          name: data.name || '',
          description: data.description || '',
          logoUrl: data.logoUrl || '',
          websiteUrl: data.websiteUrl || '',
          contactEmail: data.contactEmail || '',
          contactPhone: data.contactPhone || '',
          address: data.address || '',
          type: data.type || 'partner',
          featured: data.featured || false,
          status: data.status || 'draft',
          createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || new Date().toISOString(),
        });
      }
      
      dispatch({ type: 'SET_PARTNERS', payload: items });
    } catch (err) {
      console.error('Error fetching partners:', err);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load partners. Please try again.' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  const syncConfig = useMemo(() => ({
    storagePath: 'Partner Logos',
    collectionName: 'partners',
    processItem: async (itemRef: any, url: string, metadata: any) => {
      // Extract the partner name from the filename (without extension)
      const fileName = itemRef.name.replace(/\.[^/.]+$/, '');
      const partnerName = fileName.replace(/[-_]/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase());
      
      return {
        name: partnerName,
        logoUrl: url,
        storagePath: itemRef.fullPath,
        status: 'active',
        type: 'partner',
        featured: false,
        createdAt: metadata.timeCreated ? new Date(metadata.timeCreated).toISOString() : new Date().toISOString(),
        updatedAt: metadata.updated ? new Date(metadata.updated).toISOString() : new Date().toISOString(),
      };
    },
    transformExisting: (existingDoc: any, itemRef: any) => {
      // Only update if the storage path has changed
      if (existingDoc.storagePath !== itemRef.fullPath) {
        return {
          logoUrl: existingDoc.logoUrl, // Keep existing URL to avoid breaking existing references
          storagePath: itemRef.fullPath,
          updatedAt: new Date().toISOString(),
        };
      }
      return null; // No updates needed
    },
    onSyncComplete: (result: { success: boolean }) => {
      if (result.success) {
        fetchPartners();
      }
    },
  }), [fetchPartners]);

  const { isSyncing, handleSync } = useSyncData(syncConfig);

  useEffect(() => {
    fetchPartners().catch(console.error);
  }, [fetchPartners]);

  const handleUpdatePartner = async (id: string, updates: Partial<Partner>) => {
    dispatch({ type: 'SET_UPDATING_ID', payload: id });
    try {
      // Update in Firestore
      const partnerRef = doc(db, 'partners', id);
      await updateDoc(partnerRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });

      // Update local state
      dispatch({ 
        type: 'UPDATE_PARTNER', 
        payload: { id, updates } 
      });

      toast({
        title: 'Success',
        description: 'Partner updated successfully',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error updating partner:', error);
      toast({
        title: 'Error',
        description: 'Failed to update partner',
        variant: 'destructive',
      });
    } finally {
      dispatch({ type: 'SET_UPDATING_ID', payload: null });
    }
  };

  const handleDelete = async (id: string): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_DELETING_ID', payload: id });
      
      // Delete from Firestore
      await deleteDoc(doc(db, 'partners', id));
      
      // Update local state
      dispatch({ type: 'DELETE_PARTNER', payload: id });
      
      // Show success toast
      toast({
        title: 'Success',
        description: 'Partner deleted successfully',
      });
      
      return true;
    } catch (err) {
      console.error('Error deleting partner:', err);
      
      // Show error toast
      toast({
        title: 'Error',
        description: 'Failed to delete partner',
        variant: 'destructive',
      });
      
      return false;
    } finally {
      dispatch({ type: 'SET_DELETING_ID', payload: null });
    }
  };

  const handleDeleteCancel = () => {
    dispatch({ type: 'SHOW_DELETE_DIALOG', payload: false });
    dispatch({ type: 'SET_ITEM_TO_DELETE', payload: null });
  };

  if (loading && partners.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin text-royalBlue-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-red-600 bg-red-100 rounded-md">
        {error}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Partners</h1>
            <p className="text-sm text-muted-foreground">
              Manage your partners and their information
            </p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleSync}
              disabled={isSyncing || loading}
            >
              {isSyncing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Sync Logos
                </>
              )}
            </Button>
            <Button asChild>
              <Link href="/dashboard/partners/new">
                <Plus className="mr-2 h-4 w-4" />
                Add Partner
              </Link>
            </Button>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Manage your partners. Click "Sync Logos" to import new logos from storage.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Partner Organizations</CardTitle>
          <CardDescription>
            Manage partner organizations and their visibility on your website
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          ) : (
            <div className="rounded-md border">
              <PartnersTable 
                data={partners} 
                onDelete={handleDelete} 
                onUpdate={handleUpdatePartner}
                loading={loading}
                updatingId={updatingId}
                deletingId={deletingId}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
