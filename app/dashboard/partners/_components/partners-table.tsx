'use client';

import Link from 'next/link';
import { format } from 'date-fns';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Pencil, Trash2, ExternalLink, Loader2 } from 'lucide-react';
import { Partner } from '@/types/partner';
import Image from 'next/image';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface PartnersTableProps {
  data: Partner[];
  onDelete: (id: string) => void;
  onUpdate: (id: string, updates: Partial<Partner>) => Promise<void>;
  loading?: boolean;
  updatingId?: string | null;
  deletingId?: string | null;
}

export function PartnersTable({ 
  data, 
  onDelete, 
  onUpdate,
  loading = false,
  updatingId = null,
  deletingId = null 
}: PartnersTableProps) {
  
  const handleStatusChange = async (id: string, newStatus: 'draft' | 'published') => {
    await onUpdate(id, { status: newStatus });
  };

  const handleFeaturedChange = async (id: string, isFeatured: boolean) => {
    await onUpdate(id, { featured: isFeatured });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Loader2 className="w-8 h-8 animate-spin text-royalBlue-500" />
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[80px]">Logo</TableHead>
            <TableHead>Name</TableHead>
            <TableHead className="hidden md:table-cell">Type</TableHead>
            <TableHead className="w-[120px]">Status</TableHead>
            <TableHead className="w-[100px]">Featured</TableHead>
            <TableHead className="hidden md:table-cell">Created</TableHead>
            <TableHead className="w-[120px] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length > 0 ? (
            data.map((partner) => (
              <TableRow key={partner.id} className="hover:bg-royalBlue-500/5">
                <TableCell>
                  <div className="flex items-center">
                    {partner.logoUrl ? (
                      <div className="relative h-12 w-12 rounded-md overflow-hidden border bg-white flex items-center justify-center">
                        <Image
                          src={partner.logoUrl}
                          alt={partner.name}
                          width={48}
                          height={48}
                          className="object-contain p-1"
                          onError={(e) => {
                            // Fallback to a placeholder if image fails to load
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0OCIgaGVpZ2h0PSI0OCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiYjeDIxMjEyMTsiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cmVjdCB4PSIzIiB5PSIzIiB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHJ4PSIyIiByeT0iMiIvPjxjaXJjbGUgY3g9IjguNSIgY3k9IjguNSIgcj0iMS41Ii8+PHBhdGggZD0iTTIxIDE1bC01LjUyMi01LjE4QTMgMyAwIDAgMCAxMiAxMCIvPjwvc3ZnPg==';
                          }}
                        />
                      </div>
                    ) : (
                      <div className="h-12 w-12 rounded-md bg-muted flex items-center justify-center">
                        <span className="text-xs text-muted-foreground text-center p-1">No logo</span>
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span className="font-semibold">{partner.name}</span>
                    {partner.websiteUrl && (
                      <a 
                        href={partner.websiteUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs text-royalBlue-600 hover:underline flex items-center"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {partner.websiteUrl.replace(/^https?:\/\//, '')}
                        <ExternalLink className="w-3 h-3 ml-1" />
                      </a>
                    )}
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  <Badge variant="outline" className="capitalize">
                    {partner.type?.toLowerCase() || 'partner'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge 
                    variant={partner.status === 'published' ? 'default' : 'outline'}
                    className={partner.status === 'published' ? 'bg-green-600 hover:bg-green-700' : ''}
                  >
                    {partner.status === 'published' ? 'Published' : 'Draft'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Switch
                      checked={partner.featured}
                      onCheckedChange={(checked) => 
                        handleFeaturedChange(partner.id, checked)
                      }
                      disabled={updatingId === partner.id}
                      className={updatingId === partner.id ? 'opacity-50' : ''}
                    />
                    <span className="ml-2 text-sm text-muted-foreground hidden md:inline">
                      {partner.featured ? 'Featured' : 'Regular'}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  {partner.createdAt ? (
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(partner.createdAt), 'MMM d, yyyy')}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-1">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 w-8 p-0"
                      asChild
                    >
                      <Link href={`/partners/edit/${partner.id}`}>
                        <Pencil className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Link>
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive/90"
                      onClick={() => onDelete(partner.id)}
                      disabled={deletingId === partner.id || updatingId === partner.id}
                    >
                      {deletingId === partner.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                      <span className="sr-only">Delete</span>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                No partners found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
