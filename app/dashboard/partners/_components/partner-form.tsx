'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Loader2, X, Upload, Globe, Mail, Phone, MapPin } from 'lucide-react';
import { ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { v4 as uuidv4 } from 'uuid';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Form, FormDescription, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { db, storage } from '@/lib/firebase';
import { doc, setDoc, serverTimestamp, updateDoc, collection } from 'firebase/firestore';
import { Partner } from '@/types/partner';

// Define the form schema with proper types and defaults
const partnerFormSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  logoUrl: z.string().min(1, 'Logo is required'),
  websiteUrl: z.string().url('Please enter a valid URL').or(z.literal('')),
  contactEmail: z.string().email('Please enter a valid email').or(z.literal('')),
  contactPhone: z.string().optional(),
  address: z.string().optional(),
  type: z.string().min(1, 'Type is required'),
  featured: z.boolean(),
  status: z.enum(['draft', 'published']),
});

type PartnerFormValues = z.infer<typeof partnerFormSchema>;

interface PartnerFormProps {
  partner?: Partner;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function PartnerForm({ partner, onSuccess, onCancel }: PartnerFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  
  // State for form and UI
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [logoPreview, setLogoPreview] = useState(partner?.logoUrl || '');
  const [isDragging, setIsDragging] = useState(false);
  const isNew = !partner;
  
  // Initialize form with react-hook-form
  const form = useForm<PartnerFormValues>({
    resolver: zodResolver(partnerFormSchema),
    defaultValues: {
      name: partner?.name || '',
      description: partner?.description || '',
      logoUrl: partner?.logoUrl || '',
      websiteUrl: partner?.websiteUrl || '',
      contactEmail: partner?.contactEmail || '',
      contactPhone: partner?.contactPhone || '',
      address: partner?.address || '',
      type: partner?.type || 'partner',
      featured: partner?.featured || false,
      status: partner?.status || 'draft',
    },
    mode: 'onChange',
  });
  
  // Update logo preview when form values change
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'logoUrl') {
        setLogoPreview(value.logoUrl || '');
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  useEffect(() => {
    if (partner) {
      form.reset({
        name: partner.name,
        description: partner.description,
        logoUrl: partner.logoUrl,
        websiteUrl: partner.websiteUrl,
        contactEmail: partner.contactEmail,
        contactPhone: partner.contactPhone,
        address: partner.address,
        type: partner.type,
        featured: partner.featured,
        status: partner.status,
      });
      setLogoPreview(partner.logoUrl);
    }
  }, [partner]);

  // Handle drag and drop events
  const handleDragOver = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isUploading) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (isUploading) return;
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      const event = { target: { files } } as unknown as React.ChangeEvent<HTMLInputElement>;
      handleImageUpload(event);
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Reset file input to allow re-uploading the same file
    e.target.value = '';

    // Validate file type
    const validImageTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    if (!validImageTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a valid image file (JPEG, PNG, WebP, or GIF)',
        variant: 'destructive',
      });
      return;
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      toast({
        title: 'File too large',
        description: 'Please upload an image smaller than 5MB',
        variant: 'destructive',
      });
      return;
    }

    // Set up preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setLogoPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload to Firebase Storage
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Generate a unique filename with the original extension
      const fileExt = file.name.split('.').pop();
      const fileName = `${uuidv4()}.${fileExt}`;
      const storageRef = ref(storage, `partners/${fileName}`);
      
      // Add file metadata
      const metadata = {
        contentType: file.type,
        customMetadata: {
          originalName: file.name,
          uploadedBy: 'admin', // You can replace this with actual user ID
        },
      };

      const uploadTask = uploadBytesResumable(storageRef, file, metadata);

      uploadTask.on(
        'state_changed',
        (snapshot) => {
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          setUploadProgress(progress);
          
          // Show upload progress in toast
          if (progress < 100) {
            // Using a custom toast implementation that supports IDs
            // This is a workaround since the default toast doesn't support IDs
            const existingToast = document.getElementById('upload-progress-toast');
            if (!existingToast) {
              const toastElement = document.createElement('div');
              toastElement.id = 'upload-progress-toast';
              toastElement.className = 'fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-md shadow-lg';
              toastElement.innerHTML = `
                <h3 class="font-bold">Uploading...</h3>
                <p>Upload progress: ${Math.round(progress)}%</p>
              `;
              document.body.appendChild(toastElement);
            } else {
              const progressText = existingToast.querySelector('p');
              if (progressText) {
                progressText.textContent = `Upload progress: ${Math.round(progress)}%`;
              }
            }
          } else {
            const existingToast = document.getElementById('upload-progress-toast');
            if (existingToast) {
              existingToast.remove();
            }
          }
        },
        (error) => {
          console.error('Upload error:', error);
          toast({
            title: 'Upload failed',
            description: error.message || 'Failed to upload image. Please try again.',
            variant: 'destructive',
          });
          setLogoPreview('');
          form.setValue('logoUrl', '');
          setIsUploading(false);
        },
        async () => {
          try {
            const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
            form.setValue('logoUrl', downloadURL);
            toast({
              title: 'Upload successful',
              description: 'Image uploaded successfully',
              duration: 3000,
            });
          } catch (error: any) {
            console.error('Error getting download URL:', error);
            toast({
              title: 'Error',
              description: error.message || 'Failed to get image URL. Please try again.',
              variant: 'destructive',
            });
            setLogoPreview('');
            form.setValue('logoUrl', '');
          } finally {
            setIsUploading(false);
            setUploadProgress(0);
          }
        }
      );
    } catch (error: any) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload failed',
        description: error.message || 'An error occurred during upload. Please try again.',
        variant: 'destructive',
      });
      setLogoPreview('');
      form.setValue('logoUrl', '');
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const onSubmit = async (data: PartnerFormValues) => {
    console.log('Form submitted with data:', data);
    
    if (!data.logoUrl) {
      toast({
        title: 'Error',
        description: 'Please upload a logo',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);
      const partnerData = {
        ...data,
        updatedAt: serverTimestamp(),
      };

      if (isNew) {
        // Create new partner
        console.log('Creating new partner...');
        const newPartnerRef = doc(collection(db, 'partners'));
        await setDoc(newPartnerRef, {
          ...partnerData,
          createdAt: serverTimestamp(),
          order: 0,
        });
        
        console.log('Partner created successfully');
        toast({
          title: 'Success',
          description: 'Partner created successfully',
        });
        
        if (onSuccess) {
          onSuccess();
        } else {
          router.push('/dashboard/partners');
        }
      } else if (partner) {
        // Update existing partner
        console.log('Updating partner:', partner.id);
        await updateDoc(doc(db, 'partners', partner.id), partnerData);
        
        console.log('Partner updated successfully');
        toast({
          title: 'Success',
          description: 'Partner updated successfully',
        });
        
        if (onSuccess) onSuccess();
      }
    } catch (error) {
      console.error('Error saving partner:', error);
      toast({
        title: 'Error',
        description: 'Failed to save partner. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Left column - Logo and basic info */}
          <div className="space-y-6 lg:col-span-1">
            <div className="space-y-2">
              <Label htmlFor="logo">Logo</Label>
              <div className="flex items-center justify-center w-full">
                <label
                  htmlFor="dropzone-file"
                  className={`relative flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                    isUploading 
                      ? 'bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-600 cursor-not-allowed' 
                      : isDragging 
                        ? 'border-royalBlue-500 bg-royalBlue-50 dark:bg-royalBlue-900/20' 
                        : 'border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600'
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  {isUploading ? (
                    <div className="flex flex-col items-center justify-center p-6 text-center">
                      <Loader2 className="w-8 h-8 mb-2 text-royalBlue-500 animate-spin" />
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Uploading... {Math.round(uploadProgress)}%
                      </p>
                      <div className="w-full max-w-xs mt-4 bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div
                          className="bg-royalBlue-600 h-2.5 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        />
                      </div>
                    </div>
                  ) : logoPreview ? (
                    <div className="relative w-full h-full">
                      <div className="absolute inset-0 flex items-center justify-center p-4">
                        <img
                          src={logoPreview}
                          alt="Preview"
                          className="max-h-full max-w-full object-contain"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0OCIgaGVpZ2h0PSI0OCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM2YjcyOGIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cmVjdCB4PSIzIiB5PSIzIiB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHJ4PSIyIiByeT0iMiIvPjxjaXJjbGUgY3g9IjguNSIgY3k9IjguNSIgcj0iMS41Ii8+PHBhdGggZD0iTTIxIDE1bC01LjUyMi01LjE4QTMgMyAwIDAgMCAxMiAxMCIvPjwvc3ZnPg==';
                          }}
                        />
                      </div>
                      <div className="absolute inset-0 bg-black/40 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                        <span className="bg-white text-gray-800 text-xs font-medium px-2.5 py-1 rounded-full">
                          Click to change
                        </span>
                      </div>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          setLogoPreview('');
                          form.setValue('logoUrl', '');
                        }}
                        className="absolute -top-2 -right-2 p-1.5 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors shadow-md"
                        aria-label="Remove image"
                      >
                        <X className="w-3.5 h-3.5" />
                      </button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-6 text-center">
                      <div className="p-3 mb-3 bg-gray-200 dark:bg-gray-600 rounded-full">
                        <Upload className="w-6 h-6 text-gray-500 dark:text-gray-300" />
                      </div>
                      <p className="mb-2 text-sm text-gray-600 dark:text-gray-300">
                        <span className="font-medium text-royalBlue-600 dark:text-royalBlue-400">
                          Click to upload
                        </span>{' '}
                        or drag and drop
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        SVG, PNG, JPG or GIF (MAX. 5MB)
                      </p>
                    </div>
                  )}
                  <input
                    id="dropzone-file"
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleImageUpload}
                    disabled={isUploading}
                  />
                </label>
              </div>
              <div className="flex items-center justify-between">
                <FormDescription className="text-xs">
                  {form.watch('logoUrl') 
                    ? 'Logo uploaded successfully.' 
                    : 'Recommended: 400×400px, transparent background, .png format'}
                </FormDescription>
                {isUploading && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {Math.round(uploadProgress)}% uploaded
                  </span>
                )}
              </div>
              <FormMessage />
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="status"
                    checked={form.watch('status') === 'published'}
                    onCheckedChange={(checked) =>
                      form.setValue('status', checked ? 'published' : 'draft')
                    }
                    disabled={isLoading}
                  />
                  <Label htmlFor="status">
                    {form.watch('status') === 'published' ? 'Published' : 'Draft'}
                  </Label>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={form.watch('featured')}
                    onCheckedChange={(checked) =>
                      form.setValue('featured', checked)
                    }
                    disabled={isLoading}
                  />
                  <Label htmlFor="featured">Featured Partner</Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  Featured partners may be displayed more prominently on the website.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Partner Type</Label>
                <select
                  id="type"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  {...form.register('type')}
                  disabled={isLoading}
                >
                  <option value="partner">Partner</option>
                  <option value="sponsor">Sponsor</option>
                  <option value="supporter">Supporter</option>
                  <option value="media">Media Partner</option>
                </select>
                {form.formState.errors.type && (
                  <p className="text-sm font-medium text-destructive">
                    {form.formState.errors.type.message}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Right column - Partner details */}
          <div className="space-y-6 lg:col-span-2">
            <div className="space-y-2">
              <Label htmlFor="name">Partner Name *</Label>
              <Input
                id="name"
                placeholder="Enter partner name"
                {...form.register('name')}
                disabled={isLoading}
              />
              {form.formState.errors.name && (
                <p className="text-sm font-medium text-destructive">
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Enter a brief description of the partner"
                className="min-h-[100px]"
                {...form.register('description')}
                disabled={isLoading}
              />
              {form.formState.errors.description && (
                <p className="text-sm font-medium text-destructive">
                  {form.formState.errors.description.message}
                </p>
              )}
            </div>

            <div className="space-y-4">
              <h3 className="text-sm font-medium">Contact Information</h3>
              
              <div className="space-y-2">
                <Label htmlFor="websiteUrl" className="flex items-center">
                  <Globe className="h-4 w-4 mr-2" />
                  Website
                </Label>
                <Input
                  id="websiteUrl"
                  type="url"
                  placeholder="https://example.com"
                  {...form.register('websiteUrl')}
                  disabled={isLoading}
                />
                {form.formState.errors.websiteUrl && (
                  <p className="text-sm font-medium text-destructive">
                    {form.formState.errors.websiteUrl.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="contactEmail" className="flex items-center">
                    <Mail className="h-4 w-4 mr-2" />
                    Email
                  </Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    placeholder="<EMAIL>"
                    {...form.register('contactEmail')}
                    disabled={isLoading}
                  />
                  {form.formState.errors.contactEmail && (
                    <p className="text-sm font-medium text-destructive">
                      {form.formState.errors.contactEmail.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactPhone" className="flex items-center">
                    <Phone className="h-4 w-4 mr-2" />
                    Phone
                  </Label>
                  <Input
                    id="contactPhone"
                    type="tel"
                    placeholder="+****************"
                    {...form.register('contactPhone')}
                    disabled={isLoading}
                  />
                  {form.formState.errors.contactPhone && (
                    <p className="text-sm font-medium text-destructive">
                      {form.formState.errors.contactPhone.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address" className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2" />
                  Address
                </Label>
                <Textarea
                  id="address"
                  placeholder="123 Main St, City, State, ZIP"
                  {...form.register('address')}
                  disabled={isLoading}
                />
                {form.formState.errors.address && (
                  <p className="text-sm font-medium text-destructive">
                    {form.formState.errors.address.message}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => (onCancel ? onCancel() : router.back())}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading || isUploading}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isNew ? 'Creating...' : 'Saving...'}
              </>
            ) : isNew ? (
              'Create Partner'
            ) : (
              'Save Changes'
            )}
          </Button>
        </div>
        </form>
      </Form>
    </div>
  );
}