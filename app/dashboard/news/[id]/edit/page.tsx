"use client";
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import SimpleRichTextEditor from '@/components/ui/SimpleRichTextEditor';
import { Label } from '@/components/ui/label';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert';
import { ArrowLeft, Image as ImageIcon, Loader2, Upload } from 'lucide-react';
import Link from 'next/link';
import { News } from '@/types/news';
import { db, storage } from '@/lib/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

function stripHtml(html: string): string {
  if (!html) return '';
  return html.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ').trim();
}

function isDefaultLexicalState(content: string): boolean {
  return (
    !content ||
    content.trim() === '' ||
    content.trim() === '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}'
  );
}

function getExcerpt(news: any): string {
  if (typeof window !== 'undefined') {
    // eslint-disable-next-line no-console
    console.log('[getExcerpt] news:', news);
  }
  if (!news) {
    if (typeof window !== 'undefined') {
      // eslint-disable-next-line no-console
      console.log('[getExcerpt] news is null/undefined');
    }
    return '';
  }
  const contentText = stripHtml(news.content || '');
  const excerptText = (news.excerpt || '').trim();
  if (typeof window !== 'undefined') {
    // eslint-disable-next-line no-console
    console.log('[getExcerpt] contentText:', contentText, 'excerptText:', excerptText);
  }
  // If content is missing or default, show blank
  if (isDefaultLexicalState(news.content)) {
    if (typeof window !== 'undefined') {
      // eslint-disable-next-line no-console
      console.log('[getExcerpt] content is default lexical state, returning blank');
    }
    return '';
  }
  // If excerpt is missing, too long, or nearly identical to content, generate summary
  if (
    !excerptText ||
    excerptText.length > 200 ||
    excerptText.trim().startsWith('{') ||
    excerptText.replace(/\s+/g, '').toLowerCase() === contentText.replace(/\s+/g, '').toLowerCase() ||
    (contentText.startsWith(excerptText) && excerptText.length > 100)
  ) {
    // Only generate a summary if content isn't absurdly long or empty
    if (contentText.length > 0 && contentText.length < 5000) {
      return contentText.slice(0, 150);
    }
    return '';
  }
  // Otherwise, return the excerpt itself, capped at 150 chars
  if (typeof window !== 'undefined') {
    // eslint-disable-next-line no-console
    console.log('[getExcerpt] returning excerptText:', excerptText.slice(0, 150));
  }
  return excerptText.slice(0, 150);
}

interface EditNewsPageProps {
  params: {
    id: string;
  };
}

const EditNewsPage = ({ params }: EditNewsPageProps) => {
  const id = params?.id;
  
  if (!id) {
    throw new Error('News ID is required');
  }
  const [news, setNews] = useState<News | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<'draft' | 'published'>('draft');
  const [title, setTitle] = useState('');
  const [slug, setSlug] = useState('');
  const [excerpt, setExcerpt] = useState('');
  const [content, setContent] = useState('');
  const [featuredImage, setFeaturedImage] = useState('');
  const [notFound, setNotFound] = useState(false);
  const router = useRouter();

  async function handleImageUpload(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setUploading(true);
    setError(null);
    
    try {
      // Create a reference to the file in Firebase Storage
      const storageRef = ref(storage, `news/${Date.now()}_${file.name}`);
      
      // Upload the file
      await uploadBytes(storageRef, file);
      
      // Get the download URL
      const imageUrl = await getDownloadURL(storageRef);
      
      // Update state with the new image URL
      setFeaturedImage(imageUrl);
      
      // Show success message
      console.log('Image uploaded successfully');
    } catch (err: any) {
      console.error('Error uploading image:', err);
      setError('Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
    }
  }

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (!news) return;
    try {
      setSaving(true);
      const title = news.title.trim();
      const slug = news.slug.trim();
      const excerpt = news.excerpt.trim();
      const content = news.content || '';
      // Include the featuredImage in the update
      const featuredImage = news.featuredImage || '';
      // Ensure id is a string before passing to doc()
      const newsId = Array.isArray(id) ? id[0] : id;
      if (!newsId) throw new Error('Invalid news ID');
      const newsRef = doc(db, 'news', newsId);
      const updates: Partial<News> = {
        title,
        slug,
        excerpt,
        content,
        featuredImage,
        status,
        updatedAt: new Date().toISOString(),
      };

      // Only update publishedAt if changing from draft to published
      if (status === 'published' && news?.status !== 'published') {
        updates.publishedAt = new Date().toISOString();
      }

      await updateDoc(newsRef, updates);
      // Optionally show a success message or redirect
      router.replace('/dashboard/news');
    } catch (err: any) {
      console.error('Error saving news:', err);
      setError('Failed to save changes.');
    } finally {
      setSaving(false);
    }
  }

  useEffect(() => {
    let isMounted = true;
    async function fetchNews() {
      setLoading(true);
      setError(null);
      try {
        const newsId = params?.id;
  
  if (!newsId) {
    throw new Error('News ID is required');
  }
        if (!newsId) throw new Error('News ID is required');
        const ref = doc(db, 'news', newsId);
        const snap = await getDoc(ref);
        if (!snap.exists()) {
          if (isMounted) setNotFound(true);
        } else {
          const data = snap.data();
          // Prefer mainImage, then imageUrl, then blank
          const imageUrl = data.mainImage || data.featuredImage || '';
          if (typeof window !== 'undefined') {
            // eslint-disable-next-line no-console
            console.log('[LexicalEditor] value prop:', data.content, 'safeValue:', data.content);
            try {
              if (data.content) {
                const parsed = JSON.parse(data.content);
                // eslint-disable-next-line no-console
                console.log('[LexicalEditor] parsed safeValue:', parsed);
              }
            } catch (e) {
              // eslint-disable-next-line no-console
              console.log('[LexicalEditor] error parsing safeValue:', e);
            }
          }
          if (isMounted)
            setNews({
              id: snap.id,
              title: data.title || '',
              slug: data.slug || '',
              excerpt: data.excerpt || '',
              imagesPaths: data.imagesPaths || [], // Added required field
              content: (() => {
                const raw = typeof data.content === 'string' ? data.content : '';
                const looksLikeJson = raw.trim().startsWith('{');
                let invalid = false;
                if (looksLikeJson) {
                  try {
                    const parsed = JSON.parse(raw);
                    invalid = !parsed?.root?.children?.length;
                  } catch {
                    invalid = true;
                  }
                } else {
                  invalid = !raw || raw.length < 30;
                }
                if (invalid && typeof data.excerpt === 'string' && data.excerpt.length > 100) {
                  return data.excerpt; // fallback
                }
                return raw;
              })(),
              featuredImage: data.featuredImage || '',
              featuredImagePath: data.featuredImagePath || '', // Added optional field
              images: data.images || [],
              publishedAt: data.publishedAt || '',
              status: data.status || 'draft',
              featured: data.featured || false,
              authorId: data.authorId || '',
              authorName: data.authorName || '', // Added optional field
              categories: data.categories || [], // Added optional field
              tags: data.tags || [], // Added optional field
              viewCount: data.viewCount || 0, // Added optional field
              createdAt: data.createdAt || new Date().toISOString(),
              updatedAt: data.updatedAt || new Date().toISOString(),
            });
        }
      } catch (err: any) {
        if (isMounted) setError('Failed to load news article.');
      } finally {
        if (isMounted) setLoading(false);
      }
    }
    fetchNews();
    return () => { isMounted = false; };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params.id]);

  useEffect(() => {
    const fetchNews = async () => {
      try {
        setLoading(true);
        const newsId = Array.isArray(id) ? id[0] : id;
        if (!newsId) throw new Error('Invalid news ID');
        const docRef = doc(db, 'news', newsId);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          const data = docSnap.data() as News;
          setNews(data);
          setTitle(data.title);
          setSlug(data.slug);
          setExcerpt(data.excerpt || '');
          setContent(data.content || '');
          setFeaturedImage(data.featuredImage || '');
          setStatus(data.status || 'draft');
        } else {
          setError('News article not found');
        }
      } catch (err: any) {
        setError('Failed to load news article.');
      } finally {
        setLoading(false);
      }
    };
    if (id) {
      fetchNews();
    }
  }, [id]);

  if (!id) {
    return (
      <div className="flex items-center justify-center h-64">
        <p>Invalid news ID</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!news) {
    return (
      <div className="flex items-center justify-center h-64">
        <p>News not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <Button variant="outline" size="sm" asChild className="mb-4">
          <Link href="/dashboard/news">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to News
          </Link>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">Edit News Article</h1>
        <p className="text-muted-foreground">
          Update the details of this article
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Article Details</CardTitle>
          <CardDescription>
            Update the details for this article
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                placeholder="Enter article title"
                defaultValue={news.title}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                placeholder="article-url-slug"
                defaultValue={news.slug}
                required
              />
              <p className="text-sm text-muted-foreground">
                This will be used in the URL. Use lowercase letters, numbers, and
                hyphens.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="excerpt">Excerpt</Label>
              <Input
                id="excerpt"
                placeholder="A short summary of the article"
                defaultValue={getExcerpt(news)}
                maxLength={150}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <SimpleRichTextEditor
                value={news?.content || ''}
                onChange={val => setNews(n => n && { ...n, content: val })}
                placeholder="Write the article body..."
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="image">Featured Image</Label>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0">
                  {featuredImage ? (
                    <div className="relative h-32 w-32 overflow-hidden rounded-md border">
                      <img
                        src={featuredImage}
                        alt={news?.title || 'News thumbnail'}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="flex h-32 w-32 items-center justify-center rounded-md border border-dashed bg-muted/50">
                      <ImageIcon className="h-8 w-8 text-muted-foreground" />
                    </div>
                  )}
                </div>
                <div className="flex-1 space-y-2">
                  <div>
                    <Input
                      id="image"
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      disabled={uploading}
                      className="w-full max-w-sm"
                    />
                    <p className="mt-1 text-sm text-muted-foreground">
                      Recommended size: 1200x630px (16:9 aspect ratio)
                    </p>
                    {uploading && (
                      <div className="mt-2 flex items-center text-sm text-muted-foreground">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Uploading image...
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Status:</span>
                <div className="flex items-center space-x-2 bg-muted rounded-md p-1">
                  <button
                    type="button"
                    className={`px-3 py-1 rounded text-sm ${status === 'draft' ? 'bg-background shadow' : 'text-muted-foreground'}`}
                    onClick={() => setStatus('draft')}
                  >
                    Draft
                  </button>
                  <button
                    type="button"
                    className={`px-3 py-1 rounded text-sm ${status === 'published' ? 'bg-background shadow' : 'text-muted-foreground'}`}
                    onClick={() => setStatus('published')}
                  >
                    Publish
                  </button>
                </div>
              </div>
              <div className="flex gap-4">
                <Button variant="outline" asChild>
                  <Link href="/dashboard/news">
                    Cancel
                  </Link>
                </Button>
                <Button
                  type="submit"
                  disabled={saving}
                  className="bg-primary text-primary-foreground hover:bg-primary/90"
                >
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {status === 'draft' ? 'Saving...' : 'Publishing...'}
                    </>
                  ) : status === 'draft' ? 'Save as Draft' : 'Update and Publish'}
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditNewsPage;