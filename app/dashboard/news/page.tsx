'use client';

import { useReducer, useEffect, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { Plus, Loader2, RefreshCw } from 'lucide-react';
import { 
  collection, 
  getDocs, 
  deleteDoc, 
  doc, 
  updateDoc, 
  serverTimestamp, 
  query, 
  orderBy,
  where
} from 'firebase/firestore';
import { ref, getDownloadURL, deleteObject } from 'firebase/storage';
import { v4 as uuidv4 } from 'uuid';

import { Button } from '@/components/ui/button';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { NewsTable } from './_components/news-table';
import { News } from '@/types/news';
import { db, storage } from '@/lib/firebase';
import { useSyncData } from '@/hooks/useSyncData';

// Types
type State = {
  news: News[];
  loading: boolean;
  error: string | null;
  updatingId: string | null;
  deletingId: string | null;
  showDeleteDialog: boolean;
  itemToDelete: string | null;
  syncing: boolean;
};

type Action =
  | { type: 'SET_NEWS'; payload: News[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_UPDATING_ID'; payload: string | null }
  | { type: 'SET_DELETING_ID'; payload: string | null }
  | { type: 'SHOW_DELETE_DIALOG'; payload: boolean }
  | { type: 'SET_ITEM_TO_DELETE'; payload: string | null }
  | { type: 'UPDATE_NEWS'; payload: { id: string; updates: Partial<News> } }
  | { type: 'DELETE_NEWS'; payload: string }
  | { type: 'SET_SYNCING'; payload: boolean };

const initialState: State = {
  news: [],
  loading: true,
  error: null,
  updatingId: null,
  deletingId: null,
  showDeleteDialog: false,
  itemToDelete: null,
  syncing: false,
};

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case 'SET_NEWS':
      return { ...state, news: action.payload };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_UPDATING_ID':
      return { ...state, updatingId: action.payload };
    case 'SET_DELETING_ID':
      return { ...state, deletingId: action.payload };
    case 'SHOW_DELETE_DIALOG':
      return { ...state, showDeleteDialog: action.payload };
    case 'SET_ITEM_TO_DELETE':
      return { ...state, itemToDelete: action.payload };
    case 'SET_SYNCING':
      return { ...state, syncing: action.payload };
    case 'UPDATE_NEWS':
      return {
        ...state,
        news: state.news.map(item =>
          item.id === action.payload.id 
            ? { ...item, ...action.payload.updates } 
            : item
        ),
      };
    case 'DELETE_NEWS':
      return {
        ...state,
        news: state.news.filter(item => item.id !== action.payload),
      };
    default:
      return state;
  }
}

export default function NewsPage() {
  const { toast } = useToast();
  const [state, dispatch] = useReducer(reducer, initialState);
  const { news, loading, error, updatingId, deletingId, showDeleteDialog, itemToDelete, syncing } = state;
  
  // Get image URL from Firebase Storage
  const getImageUrl = useCallback(async (storagePath: string): Promise<string> => {
    if (!storagePath) return '';
    try {
      const imageRef = ref(storage, storagePath);
      return await getDownloadURL(imageRef);
    } catch (error) {
      console.error('Error getting image URL:', error);
      return '';
    }
  }, []);

  // Fetch news from Firestore
  const fetchNews = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });
    
    try {
      console.log('[NewsPage] Fetching news from Firestore...');
      const q = query(collection(db, 'news'), orderBy('publishedAt', 'desc'));
      const snapshot = await getDocs(q);
      
      const items: News[] = [];
      
      for (const doc of snapshot.docs) {
        try {
          const data = doc.data();
          
          // Skip if required fields are missing or invalid
          if (!data || typeof data !== 'object' || 
              typeof data.title !== 'string' || 
              !data.publishedAt) {
            console.warn(`Skipping news item ${doc.id}: Invalid data structure`);
            continue;
          }
          
          // Get URLs for featured image and images array
          const featuredImageUrl = data.featuredImagePath ? await getImageUrl(data.featuredImagePath) : '';
          
          // Get URLs for all images in the images array
          const imageUrls = await Promise.all(
            (data.imagesPaths || []).map(async (path: string) => ({
              path,
              url: await getImageUrl(path)
            }))
          );
          
          const newsItem: News = {
            id: doc.id,
            title: data.title,
            slug: data.slug || '',
            excerpt: data.excerpt || data.content?.substring(0, 200) + '...' || '',
            content: data.content || '',
            featuredImage: featuredImageUrl,
            featuredImagePath: data.featuredImagePath || '',
            images: imageUrls.map(img => img.url).filter(Boolean),
            imagesPaths: data.imagesPaths || [],
            status: data.status || 'draft',
            featured: data.featured || false,
            authorId: data.authorId || '',
            authorName: data.authorName || 'Admin',
            categories: data.categories || [],
            tags: data.tags || [],
            viewCount: data.viewCount || 0,
            publishedAt: data.publishedAt.toDate ? data.publishedAt.toDate().toISOString() : new Date().toISOString(),
            updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate().toISOString() : new Date().toISOString(),
            createdAt: data.createdAt?.toDate ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
          };
          
          items.push(newsItem);
          
        } catch (error) {
          console.error(`Error processing news item ${doc.id}:`, error);
        }
      }
      
      console.log(`[NewsPage] Fetched ${items.length} news items`);
      dispatch({ type: 'SET_NEWS', payload: items });
      return items;
    } catch (error) {
      console.error('Error fetching news:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load news. Please try again.' });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [getImageUrl]);
  
  // Sync configuration for news images
  const syncConfig = useMemo(() => ({
    collectionName: 'news',
    storagePath: 'news', // Changed to match the root news folder
    processItem: async (itemRef: any, url: string, metadata: any) => {
      // Get the image name without extension
      const imageName = itemRef.name.replace(/\.[^/.]+$/, '');
      
      // Create a news item with the image name as the title
      return {
        title: imageName.replace(/[-_]/g, ' '), // Convert underscores and hyphens to spaces
        excerpt: '',
        content: '',
        featuredImage: url,
        featuredImagePath: itemRef.fullPath,
        images: [url],
        imagesPaths: [itemRef.fullPath],
        status: 'draft',
        featured: false,
        authorId: 'system',
        authorName: 'System',
        categories: [],
        tags: [],
        viewCount: 0,
        publishedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString()
      };
    },
    transformExisting: (existingDoc: any, itemRef: any, metadata: any) => {
      const data = existingDoc.data();
      const imageUrl = metadata.url;
      
      // Check if this image is already in the news item
      const imageIndex = data.imagesPaths?.indexOf(itemRef.fullPath) ?? -1;
      
      // If image is not in the array, add it
      if (imageIndex === -1) {
        return {
          ...data,
          featuredImage: data.featuredImage || imageUrl,
          featuredImagePath: data.featuredImagePath || itemRef.fullPath,
          images: [...(data.images || []), imageUrl],
          imagesPaths: [...(data.imagesPaths || []), itemRef.fullPath],
          updatedAt: new Date().toISOString(),
        };
      }
      
      return data;
    },
    onSyncComplete: (result: { success: boolean }) => {
      if (result.success) {
        fetchNews();
      }
    }
  }), [getImageUrl, fetchNews]);

  const { isSyncing: isSyncingData, handleSync } = useSyncData(syncConfig);
  
  // Handle sync button click
  const handleSyncClick = useCallback(async () => {
    try {
      dispatch({ type: 'SET_SYNCING', payload: true });
      await handleSync();
      // Refresh the news after sync
      await fetchNews();
      toast({
        title: 'Success',
        description: 'News synced successfully',
      });
    } catch (error) {
      console.error('Error syncing news:', error);
      toast({
        title: 'Error',
        description: 'Failed to sync news',
        variant: 'destructive',
      });
    } finally {
      dispatch({ type: 'SET_SYNCING', payload: false });
    }
  }, [handleSync, toast, fetchNews]);
  
  // Handle delete news item
  const handleDelete = useCallback(async (id: string) => {
    dispatch({ type: 'SET_ITEM_TO_DELETE', payload: id });
    dispatch({ type: 'SHOW_DELETE_DIALOG', payload: true });
  }, []);
  
  // Confirm delete action
  const confirmDelete = useCallback(async () => {
    if (!itemToDelete) return;
    
    dispatch({ type: 'SET_DELETING_ID', payload: itemToDelete });
    
    try {
      // Get the item to delete
      const item = news.find(item => item.id === itemToDelete);
      
      if (item) {
        // Delete the image from storage if it exists
        if (item.featuredImagePath) {
          try {
            const imageRef = ref(storage, item.featuredImagePath);
            await deleteObject(imageRef);
          } catch (error) {
            console.error('Error deleting image:', error);
            // Continue with deletion even if image deletion fails
          }
        }
        
        // Delete any additional images
        if (item.imagesPaths && item.imagesPaths.length > 0) {
          await Promise.all(
            item.imagesPaths.map(async (path: string) => {
              try {
                const imageRef = ref(storage, path);
                await deleteObject(imageRef);
              } catch (error) {
                console.error(`Error deleting image at path ${path}:`, error);
              }
            })
          );
        }
        
        // Delete the document from Firestore
        await deleteDoc(doc(db, 'news', itemToDelete));
        
        // Update local state
        dispatch({ type: 'DELETE_NEWS', payload: itemToDelete });
        
        toast({
          title: 'Success',
          description: 'News item deleted successfully',
        });
      }
    } catch (error) {
      console.error('Error deleting news item:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete news item',
        variant: 'destructive',
      });
    } finally {
      dispatch({ type: 'SET_DELETING_ID', payload: null });
      dispatch({ type: 'SHOW_DELETE_DIALOG', payload: false });
      dispatch({ type: 'SET_ITEM_TO_DELETE', payload: null });
    }
  }, [itemToDelete, news, toast]);
  
  // Handle update news item
  const handleUpdate = useCallback(async (id: string, updates: Partial<Omit<News, 'id' | 'createdAt' | 'updatedAt' | 'publishedAt'>>) => {
    dispatch({ type: 'SET_UPDATING_ID', payload: id });
    
    try {
      const newsRef = doc(db, 'news', id);
      await updateDoc(newsRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });
      
      // Update local state
      dispatch({
        type: 'UPDATE_NEWS',
        payload: {
          id,
          updates: {
            ...updates,
            updatedAt: new Date().toISOString(),
          },
        },
      });
      
      toast({
        title: 'Success',
        description: 'News item updated successfully',
      });
      
      return true;
    } catch (error) {
      console.error('Error updating news item:', error);
      toast({
        title: 'Error',
        description: 'Failed to update news item',
        variant: 'destructive',
      });
      return false;
    } finally {
      dispatch({ type: 'SET_UPDATING_ID', payload: null });
    }
  }, [toast]);
  
  // Toggle featured status
  const toggleFeatured = useCallback(async (id: string, currentStatus: boolean): Promise<void> => {
    dispatch({ type: 'SET_UPDATING_ID', payload: id });
    
    try {
      const newsRef = doc(db, 'news', id);
      await updateDoc(newsRef, {
        featured: !currentStatus,
        updatedAt: serverTimestamp(),
      });
      
      // Update local state
      dispatch({
        type: 'UPDATE_NEWS',
        payload: {
          id,
          updates: {
            featured: !currentStatus,
            updatedAt: new Date().toISOString(),
          },
        },
      });
      
      toast({
        title: 'Success',
        description: `News item marked as ${!currentStatus ? 'featured' : 'not featured'}`,
      });
      
      return Promise.resolve();
    } catch (error) {
      console.error('Error toggling featured status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update featured status',
        variant: 'destructive',
      });
      return Promise.reject(error);
    } finally {
      dispatch({ type: 'SET_UPDATING_ID', payload: null });
    }
  }, [toast]);
  
  // Fetch news on component mount
  useEffect(() => {
    fetchNews();
  }, [fetchNews]);

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">News</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleSyncClick}
            disabled={syncing || isSyncingData}
          >
            {syncing || isSyncingData ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Sync
              </>
            )}
          </Button>
          <Button asChild>
            <Link href="/news/create">
              <Plus className="mr-2 h-4 w-4" />
              Add News
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage News</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-destructive">{error}</p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={fetchNews}
              >
                Retry
              </Button>
            </div>
          ) : (
            <NewsTable
              data={news}
              onDelete={handleDelete}
              onUpdate={handleUpdate}
              onToggleFeatured={toggleFeatured}
              updatingId={updatingId}
              deletingId={deletingId}
            />
          )}
        </CardContent>
      </Card>

      <AlertDialog open={showDeleteDialog} onOpenChange={(open) => !open && dispatch({ type: 'SHOW_DELETE_DIALOG', payload: open })}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the news item and remove all associated images.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={!!deletingId}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete}
              disabled={!!deletingId}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deletingId === itemToDelete ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
