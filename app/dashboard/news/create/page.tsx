"use client";
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import SimpleRichTextEditor from '@/components/ui/SimpleRichTextEditor'
import { useState } from 'react'
import { ArrowLeft, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { db, storage } from '@/lib/firebase'
import { collection, addDoc } from 'firebase/firestore'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'

export default function CreateNewsPage() {
  const router = useRouter();
  const [content, setContent] = useState<string>("");
  const [title, setTitle] = useState<string>("");
  const [slug, setSlug] = useState<string>("");
  const [excerpt, setExcerpt] = useState<string>("");
  const [imageUrl, setImageUrl] = useState<string>("");
  const [uploading, setUploading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<'draft' | 'published'>('draft');
  
  // Handle image upload
  async function handleImageUpload(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setUploading(true);
    setError(null);
    
    try {
      // Create a reference to the file in Firebase Storage
      const storageRef = ref(storage, `news/${Date.now()}_${file.name}`);
      
      // Upload the file
      await uploadBytes(storageRef, file);
      
      // Get the download URL
      const url = await getDownloadURL(storageRef);
      
      // Update state with the new image URL
      setImageUrl(url);
      
      console.log('Image uploaded successfully');
    } catch (err: any) {
      console.error('Error uploading image:', err);
      setError('Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
    }
  }
  
  // Handle form submission
  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setSaving(true);
    setError(null);
    
    try {
      // Create a new news document in Firestore
      await addDoc(collection(db, 'news'), {
        title,
        slug,
        excerpt,
        content,
        featuredImage: imageUrl,
        status,
        publishedAt: status === 'published' ? new Date().toISOString() : null,
        featured: false,
        viewCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
      
      console.log('News article created successfully');
      router.push('/dashboard/news');
    } catch (err: any) {
      console.error('Error creating news article:', err);
      setError('Failed to create news article. Please try again.');
    } finally {
      setSaving(false);
    }
  }
  return (
    <div className="space-y-6">
      <div>
        <Button variant="outline" size="sm" asChild className="mb-4">
          <Link href="/dashboard/news">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to News
          </Link>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">Create News Article</h1>
        <p className="text-muted-foreground">
          Add a new article to the news section
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Article Details</CardTitle>
          <CardDescription>
            Fill in the details for your new article
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input 
                id="title" 
                placeholder="Enter article title" 
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required 
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                placeholder="article-url-slug"
                value={slug}
                onChange={(e) => setSlug(e.target.value)}
                required
              />
              <p className="text-sm text-muted-foreground">
                This will be used in the URL. Use lowercase letters, numbers, and
                hyphens.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="excerpt">Excerpt</Label>
              <Textarea
                id="excerpt"
                placeholder="A short summary of the article"
                rows={3}
                value={excerpt}
                onChange={(e) => setExcerpt(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <SimpleRichTextEditor
                value={content}
                onChange={setContent}
                className="font-mono text-sm"
              />
              {/* Add extra space below the editor to prevent overlap */}
              <div className="mb-8" />
            </div>  

            <div className="space-y-2">
              <Label htmlFor="image">Featured Image</Label>
              <div className="flex items-center space-x-4">
                {imageUrl ? (
                  <div className="relative h-24 w-24 overflow-hidden rounded-md border">
                    <img
                      src={imageUrl}
                      alt="Featured image"
                      className="h-full w-full object-cover"
                    />
                  </div>
                ) : null}
                <div className="flex-1">
                  <Input 
                    id="image" 
                    type="file" 
                    accept="image/*" 
                    className="w-auto" 
                    onChange={handleImageUpload}
                    disabled={uploading}
                  />
                  {uploading && (
                    <div className="flex items-center mt-2 text-sm text-muted-foreground">
                      <Loader2 className="h-3 w-3 animate-spin mr-2" />
                      Uploading image...
                    </div>
                  )}
                </div>
              </div>
              {error && <p className="text-sm text-red-500 mt-2">{error}</p>}
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Status:</span>
                <div className="flex items-center space-x-2 bg-muted rounded-md p-1">
                  <button
                    type="button"
                    className={`px-3 py-1 rounded text-sm ${status === 'draft' ? 'bg-background shadow' : 'text-muted-foreground'}`}
                    onClick={() => setStatus('draft')}
                  >
                    Draft
                  </button>
                  <button
                    type="button"
                    className={`px-3 py-1 rounded text-sm ${status === 'published' ? 'bg-background shadow' : 'text-muted-foreground'}`}
                    onClick={() => setStatus('published')}
                  >
                    Publish
                  </button>
                </div>
              </div>
              <div className="flex gap-4">
                <Button variant="outline" asChild>
                  <Link href="/dashboard/news">
                    Cancel
                  </Link>
                </Button>
                <Button
                  type="submit"
                  disabled={saving}
                  className="bg-primary text-primary-foreground hover:bg-primary/90"
                >
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {status === 'draft' ? 'Saving Draft...' : 'Publishing...'}
                    </>
                  ) : status === 'draft' ? 'Save as Draft' : 'Publish Now'}
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}