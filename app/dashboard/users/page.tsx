"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  MoreVertical, Pencil, Search, User, UserCheck, UserX, Shield, UserPlus 
} from "lucide-react";
import UsersTable from './components/UsersTable'; // Added import for UsersTable
import { auth, db } from "@/lib/firebase";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { useAdminAuth } from "@/contexts/AdminAuthContext";
import { toast } from "../_lib/toast";
import { EditUserDialog } from "./_components/EditUserDialog";
import { AdminUser, UserRole } from "@/lib/types/user";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Using AdminUser from @/lib/types/user

export default function UsersPage() {
  const { user: currentUser } = useAdminAuth();
  // Using local toast implementation
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingUser, setEditingUser] = useState<AdminUser | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const usersPerPage = 10;

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/admin/users');
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      
      const data = await response.json();
      setUsers(data);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Could not fetch users. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageUpload = async (file: File) => {
    if (!editingUser) return null;
    
    try {
      setIsUploading(true);
      
      // Create a unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${editingUser.uid}-${Date.now()}.${fileExt}`;
      const storagePath = `user-avatars/${fileName}`;
      
      // Import Firebase Storage v9 modular API
      const { getStorage, ref, uploadBytes, getDownloadURL } = await import('firebase/storage');
      const storage = getStorage();
      const storageRef = ref(storage, storagePath);
      
      // Upload the file
      await uploadBytes(storageRef, file);
      
      // Get the download URL
      const downloadURL = await getDownloadURL(storageRef);
      
      // Update the user's photoURL
      const response = await fetch(`/api/admin/users`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uid: editingUser.uid,
          photoURL: downloadURL,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update profile picture');
      }
      
      // Refresh users list
      await fetchUsers();
      return downloadURL;
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload image. Please try again.',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  // Fetch admin users from Firestore
  useEffect(() => {
    if (currentUser) {
      fetchUsers();
    }
  }, [currentUser]);

  // Filter users based on search term
  const filteredUsers = users.filter(user => 
    user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.displayName?.toLowerCase().includes(searchTerm.toLowerCase())
  ) as AdminUser[];

  // Filter users based on current user's permissions
  const filteredAndAuthorizedUsers = filteredUsers.filter(user => {
    // Super admins can see everyone
    if (currentUser?.role === 'super_admin') return true;
    
    // Admins can see other admins and editors
    if (currentUser?.role === 'admin') {
      return user.role !== 'super_admin';
    }
    
    // Editors can only see themselves
    return user.uid === currentUser?.uid;
  });

  // Pagination variables
  const indexOfLastUser = currentPage * usersPerPage;
  const indexOfFirstUser = indexOfLastUser - usersPerPage;
  const currentUsers = filteredAndAuthorizedUsers.slice(indexOfFirstUser, indexOfLastUser);
  const totalPages = Math.ceil(filteredAndAuthorizedUsers.length / usersPerPage);

  // Handle user status toggle
  const toggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      // Check if trying to modify another super admin (only super admins can do this)
      const targetUser = users.find(u => u.uid === userId);
      if (targetUser?.role === 'super_admin' && currentUser?.role !== 'super_admin') {
        toast({
          title: "Permission Denied",
          description: "Only super admins can modify other super admins.",
          variant: "destructive",
        });
        return;
      }

      // Update in Firestore
      const userDoc = doc(db, 'adminUser', userId);
      await updateDoc(userDoc, { disabled: !currentStatus });
      
      // Update local state
      setUsers(users.map(user => 
        user.uid === userId ? { ...user, disabled: !currentStatus } : user
      ));
      
      toast({
        title: "Success",
        description: `User has been ${!currentStatus ? 'activated' : 'deactivated'}.`,
      });
    } catch (error) {
      console.error("Error updating user status:", error);
      toast({
        title: "Error",
        description: "Failed to update user status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle role change
  const updateUserRole = async (userId: string, newRole: UserRole) => {
    try {
      // Check if trying to modify a super admin (only super admins can do this)
      const targetUser = users.find(u => u.uid === userId);
      if (targetUser?.role === 'super_admin' && currentUser?.role !== 'super_admin') {
        toast({
          title: "Permission Denied",
          description: "Only super admins can modify other super admins.",
          variant: "destructive",
        });
        return;
      }

      // Prevent changing the last super admin to a different role
      if (targetUser?.role === 'super_admin' && newRole !== 'super_admin') {
        const superAdminCount = users.filter(u => u.role === 'super_admin').length;
        if (superAdminCount <= 1) {
          toast({
            title: "Cannot Remove Last Super Admin",
            description: "There must be at least one super admin.",
            variant: "destructive",
          });
          return;
        }
      }

      // Update in Firestore
      const userDoc = doc(db, 'adminUser', userId);
      await updateDoc(userDoc, { role: newRole });
      
      // Update local state
      setUsers(users.map(user => 
        user.uid === userId ? { ...user, role: newRole } : user
      ));
      
      toast({
        title: "Success",
        description: `User role has been updated to ${newRole.replace('_', ' ')}.`,
      });
    } catch (error) {
      console.error("Error updating user role:", error);
      toast({
        title: "Error",
        description: "Failed to update user role. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Check if current user can edit a specific user
  const canEditUser = (targetUser: AdminUser | null) => {
    if (!targetUser || !currentUser) return false;
    
    // Super admins can edit anyone
    if (currentUser.role === 'super_admin') return true;
    
    // Admins can only edit non-super_admins
    if (currentUser.role === 'admin') {
      return targetUser.role !== 'super_admin';
    }
    
    // Editors can't edit anyone
    return false;
  };
  
  // Get available roles based on current user's role
  const getAvailableRoles = () => {
    if (currentUser?.role === 'super_admin') {
      return [
        { value: 'super_admin', label: 'Super Admin' },
        { value: 'admin', label: 'Admin' },
        { value: 'editor', label: 'Editor' },
      ];
    }
    return [
      { value: 'admin', label: 'Admin' },
      { value: 'editor', label: 'Editor' },
    ];
  };

  const handleEditUser = (user: AdminUser) => {
    setEditingUser(user);
    setIsEditDialogOpen(true);
  };

  const handleUserUpdated = () => {
    fetchUsers(); // Refresh the user list
  };

  const handleAddUser = () => {
    // Implement add user functionality
  };

  ;
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">User Management</h1>
          <p className="text-sm text-gray-600 mt-1">
            Manage admin users and their permissions
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search users..."
              className="pl-10 bg-white border-gray-300 text-gray-900 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          {(currentUser?.role === 'super_admin' || currentUser?.role === 'admin') && (
            <Button 
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
              onClick={handleAddUser}
            >
              <UserPlus className="h-4 w-4" />
              <span className="hidden sm:inline">Add User</span>
            </Button>
          )}
        </div>
      </div>

      <Card className="bg-white/90 backdrop-blur-sm border border-gray-200 shadow-lg overflow-hidden">
        <div className="p-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
            </div>
          ) : (
            <UsersTable
              currentUsers={currentUsers}
              currentUser={currentUser}
              onEditUser={handleEditUser}
              onToggleUserStatus={toggleUserStatus}
              onUpdateUserRole={updateUserRole}
              getAvailableRoles={getAvailableRoles}
              indexOfFirstUser={indexOfFirstUser}
              indexOfLastUser={indexOfLastUser}
              totalUsers={filteredAndAuthorizedUsers.length}
              currentPage={currentPage}
              totalPages={totalPages}
              setCurrentPage={setCurrentPage}
            />
          )}
        </div>
      </Card>
      
      {/* Edit User Dialog */}
      {editingUser && (
        <EditUserDialog
          user={editingUser}
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onUserUpdated={() => {
            handleUserUpdated(); // Always fetch latest users after update
            setTimeout(fetchUsers, 500); // Also re-fetch after a short delay to ensure Storage/Firestore sync
          }}
          onImageUpload={handleImageUpload}
          isUploading={isUploading}
        />
      )}
    </div>
  );
}
