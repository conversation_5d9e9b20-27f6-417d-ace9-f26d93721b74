"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, User as UserIcon, Camera } from "lucide-react";
import { toast } from "../../_lib/toast";
import { doc, updateDoc } from "firebase/firestore";
import { auth, db } from "@/lib/firebase";
import { EmailAuthProvider, reauthenticateWithCredential, updateEmail, updatePassword, updateProfile, User as FirebaseUser } from "firebase/auth";
import { AdminUser, UpdateUserData, UserRole } from "@/lib/types/user";

interface EditUserDialogProps {
  /** The user being edited */
  user: AdminUser | null;
  
  /** Whether the dialog is open */
  open: boolean;
  
  /** Callback when dialog open state changes */
  onOpenChange: (open: boolean) => void;
  
  /** Callback when user is successfully updated */
  onUserUpdated: () => void;
  
  /** Callback for image upload */
  onImageUpload?: (file: File) => Promise<string | null>;
  
  /** Whether an image is currently being uploaded */
  isUploading?: boolean;
}

export function EditUserDialog({ 
  user, 
  open, 
  onOpenChange, 
  onUserUpdated, 
  onImageUpload,
  isUploading = false 
}: EditUserDialogProps) {
  const [displayName, setDisplayName] = useState("");
  const [email, setEmail] = useState("");
  const [role, setRole] = useState<UserRole>('editor' as UserRole);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [photoURL, setPhotoURL] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isPasswordSectionOpen, setIsPasswordSectionOpen] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (user) {
      setDisplayName(user.displayName || "");
      setEmail(user.email || "");
      setRole(user.role || "editor");
      setPhotoURL(user.photoURL || null);
    }
  }, [user]);

  const validate = () => {
    const newErrors: Record<string, string> = {};
    
    if (!displayName.trim()) {
      newErrors.displayName = "Display name is required";
    }
    
    if (!email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Email is invalid";
    }
    
    if (isPasswordSectionOpen && newPassword) {
      if (newPassword.length < 6) {
        newErrors.newPassword = "Password must be at least 6 characters";
      } else if (newPassword !== confirmPassword) {
        newErrors.confirmPassword = "Passwords do not match";
      }
      
      if (!currentPassword) {
        newErrors.currentPassword = "Current password is required to change password";
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && onImageUpload) {
      try {
        const url = await onImageUpload(file);
        if (url) {
          setPhotoURL(url);
        }
      } catch (error) {
        console.error("Error uploading image:", error);
        toast({
          title: "Error",
          description: "Failed to upload image. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !validate()) return;
    
    try {
      setIsLoading(true);
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        throw new Error('No authenticated user found');
      }
      
      // Reauthenticate if changing sensitive data
      if (email !== user.email || newPassword) {
        const credential = EmailAuthProvider.credential(
          currentUser.email || user.email || '',
          currentPassword
        );
        await reauthenticateWithCredential(currentUser, credential);
      }
      
      // Update email if changed
      if (email !== user.email) {
        await updateEmail(currentUser, email);
      }
      
      // Update password if provided
      if (newPassword) {
        await updatePassword(currentUser, newPassword);
      }
      
      // Update profile (displayName, photoURL)
      const profileUpdates: { displayName: string | null; photoURL?: string | null } = {
        displayName,
      };
      
      // Only include photoURL if it's not null or empty string
      if (photoURL !== null && photoURL !== '') {
        profileUpdates.photoURL = photoURL;
      } else if (photoURL === '') {
        // If photoURL is empty string, explicitly set to null to remove it
        profileUpdates.photoURL = null;
      }
      
      await updateProfile(currentUser, profileUpdates);
      
      // Only use the API for role changes (admin operation)
      if (role !== user.role) {
        const response = await fetch(`/api/admin/users`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            uid: user.uid,
            role,
            updatedAt: new Date().toISOString(),
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to update user role');
        }
      }
      
              // Update Firestore user document with proper type conversion
      // Prepare update payload
      const updateData: Record<string, any> = {
        uid: user.uid,
        displayName: displayName || null,
        photoURL: photoURL || null,
      };
      if (email) updateData.email = email;
      // Password change is not handled here; add if needed
      
      const response = await fetch('/api/admin/users', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData),
      });
      if (!response.ok) {
        const err = await response.json();
        throw new Error(err.error || 'Failed to update user');
      }
      toast({
        title: "Success",
        description: "Profile updated successfully",
      });
      onUserUpdated();
      onOpenChange(false);
    } catch (error: any) {
      console.error("Error updating user:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update user. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] bg-white">
        <div className="fixed inset-0 bg-black/50 z-[-1]" />
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            <div className="flex flex-col items-center space-y-4">
              <div className="relative group">
                <div className="w-24 h-24 rounded-full bg-gray-200 overflow-hidden border-4 border-white shadow-md">
                  {photoURL ? (
                    <img
                      src={photoURL}
                      alt={displayName || 'User'}
                      className="w-full h-full object-cover cursor-pointer group-hover:opacity-80 transition"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-300 cursor-pointer group-hover:opacity-80 transition">
                      <UserIcon className="w-12 h-12 text-gray-500" />
                    </div>
                  )}
                  {onImageUpload && (
                    <label className="absolute bottom-2 right-2 bg-white rounded-full p-2 shadow-md cursor-pointer hover:bg-gray-100 border border-gray-200 transition">
                      <Camera className="w-5 h-5 text-gray-600" />
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleImageChange}
                        disabled={isUploading}
                      />
                    </label>
                  )}
                </div>
                <span className="block text-xs text-gray-500 mt-2">Click avatar to change</span>
              </div>
              {isUploading && <p className="text-sm text-blue-500 animate-pulse">Uploading image...</p>}
            </div>

            <div className="space-y-4">
              <Label htmlFor="displayName">Display Name</Label>
              <Input
                id="displayName"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                disabled={isLoading}
              />
              {errors.displayName && (
                <p className="text-sm text-red-500 mt-1">{errors.displayName}</p>
              )}
            </div>
            
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
              />
              {errors.email && (
                <p className="text-sm text-red-500 mt-1">{errors.email}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <select
                id="role"
                value={role}
                onChange={(e) => setRole(e.target.value as UserRole)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                disabled={isLoading}
              >
                <option value="super_admin">Super Admin</option>
                <option value="admin">Admin</option>
                <option value="editor">Editor</option>
              </select>
            </div>
            
            <div className="pt-4 border-t">
              <button
                type="button"
                className="text-sm font-medium text-blue-600 hover:underline"
                onClick={() => setIsPasswordSectionOpen(!isPasswordSectionOpen)}
              >
                {isPasswordSectionOpen ? 'Hide' : 'Change Password'}
              </button>
            
              {isPasswordSectionOpen && (
                <div className="mt-4 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">Current Password</Label>
                    <Input
                      id="currentPassword"
                      type="password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      disabled={isLoading}
                    />
                    {errors.currentPassword && (
                      <p className="text-sm text-red-500">{errors.currentPassword}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <Input
                      id="newPassword"
                      type="password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      disabled={isLoading}
                    />
                    {errors.newPassword && (
                      <p className="text-sm text-red-500">{errors.newPassword}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      disabled={isLoading}
                    />
                    {errors.confirmPassword && (
                      <p className="text-sm text-red-500">{errors.confirmPassword}</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
