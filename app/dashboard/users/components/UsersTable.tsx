'use client';

import React from 'react';
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User, UserCheck, UserX, MoreHorizontal, ShieldCheck, ShieldAlert, Edit3 } from 'lucide-react';
import { AdminUser, UserRole } from '@/lib/types/user'; // Corrected path for AdminUser

interface UsersTableProps {
  currentUsers: AdminUser[];
  currentUser: AdminUser | null;
  onEditUser: (user: AdminUser) => void;
  onToggleUserStatus: (userId: string, currentStatus: boolean) => void;
  onUpdateUserRole: (userId: string, newRole: UserRole) => Promise<void> | void;
  getAvailableRoles: () => { value: string; label: string }[];
  indexOfFirstUser: number;
  indexOfLastUser: number;
  totalUsers: number;
  currentPage: number;
  totalPages: number;
  setCurrentPage: (page: number | ((prevPage: number) => number)) => void;
}

const UsersTable: React.FC<UsersTableProps> = ({
  currentUsers,
  currentUser,
  onEditUser,
  onToggleUserStatus,
  onUpdateUserRole,
  getAvailableRoles,
  indexOfFirstUser,
  indexOfLastUser,
  totalUsers,
  currentPage,
  totalPages,
  setCurrentPage,
}) => {
  const canPerformAction = (targetUser: AdminUser) => {
    if (!currentUser) return false;
    if (currentUser.role === 'super_admin') {
      return targetUser.role !== 'super_admin' || targetUser.uid === currentUser.uid;
    }
    if (currentUser.role === 'admin') {
      return targetUser.role !== 'super_admin' && targetUser.role !== 'admin';
    }
    return false;
  };

  return (
    <>
      <div className="rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden shadow-md">
        <Table className="min-w-full bg-white dark:bg-gray-800">
          <TableHeader className="bg-gray-50 dark:bg-gray-700">
            <TableRow className="hover:bg-transparent dark:hover:bg-transparent">
              <TableHead className="text-gray-600 dark:text-gray-300 font-semibold">User</TableHead>
              <TableHead className="text-gray-600 dark:text-gray-300 font-semibold">Email</TableHead>
              <TableHead className="text-gray-600 dark:text-gray-300 font-semibold">Status</TableHead>
              <TableHead className="text-gray-600 dark:text-gray-300 font-semibold">Role</TableHead>
              <TableHead className="text-gray-600 dark:text-gray-300 font-semibold">Last Active</TableHead>
              <TableHead className="text-gray-600 dark:text-gray-300 font-semibold text-right pr-4">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="divide-y divide-gray-200 dark:divide-gray-700">
            {currentUsers.length > 0 ? (
              currentUsers.map((user) => {
                console.log(`[UsersTable] Rendering user: ${user.displayName}, Metadata:`, user.metadata);
                return (
                  <TableRow key={user.uid} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 group transition-colors">
                    <TableCell className="font-medium text-gray-900 dark:text-gray-100 py-3">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10 border border-gray-200 dark:border-gray-600">
                          <AvatarImage src={user.photoURL || undefined} alt={user.displayName || 'User'} />
                          <AvatarFallback className="bg-gray-200 dark:bg-gray-600">
                            <User className="h-5 w-5 text-gray-400 dark:text-gray-300" />
                          </AvatarFallback>
                        </Avatar>
                        <div className="min-w-0">
                          <p className="truncate font-semibold text-sm">
                            {user.displayName?.trim() || 'No Name'}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            ID: {user.uid.substring(0, 8)}...
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm text-gray-500 dark:text-gray-400">{user.email}</TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          user.disabled
                            ? 'bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-100'
                            : 'bg-green-100 text-green-800 dark:bg-green-700 dark:text-green-100'
                        }`}
                      >
                        {user.disabled ? 'Inactive' : 'Active'}
                      </span>
                    </TableCell>
                    <TableCell className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                      {user.role?.replace('_', ' ') || 'N/A'}
                    </TableCell>
                    <TableCell className="text-sm text-gray-500 dark:text-gray-400">
                      {user.metadata?.lastSignInTime
                        ? new Date(user.metadata.lastSignInTime).toLocaleDateString()
                        : 'Never'}
                    </TableCell>
                    <TableCell className="text-right py-3 pr-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-5 w-5" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700">
                          <DropdownMenuItem
                            className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200"
                            onClick={() => onEditUser(user)}
                          >
                            <Edit3 className="mr-2 h-4 w-4" /> Edit User
                          </DropdownMenuItem>
                          {canPerformAction(user) && (
                            <>
                              <DropdownMenuSeparator className="dark:bg-gray-700" />
                              <DropdownMenuItem
                                className={`cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${
                                  user.disabled
                                    ? 'text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/50'
                                    : 'text-yellow-600 dark:text-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/50'
                                }`}
                                onClick={() => onToggleUserStatus(user.uid, user.disabled)}
                              >
                                {user.disabled ? (
                                  <UserCheck className="mr-2 h-4 w-4" />
                                ) : (
                                  <UserX className="mr-2 h-4 w-4" />
                                )}
                                {user.disabled ? 'Enable User' : 'Disable User'}
                              </DropdownMenuItem>
                              
                              {getAvailableRoles().length > 0 && (
                                <>
                                  <DropdownMenuSeparator className="dark:bg-gray-700"/>
                                  <p className="px-2 py-1.5 text-xs font-semibold text-gray-500 dark:text-gray-400">Change Role:</p>
                                  {getAvailableRoles()
                                    .filter(role => {
                                      if (currentUser?.role === 'super_admin') return role.value !== 'super_admin';
                                      if (currentUser?.role === 'admin') return role.value === 'editor';
                                      return false;
                                    })
                                    .map((role) => (
                                    <DropdownMenuItem
                                      key={role.value}
                                      className={`cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${user.role === role.value ? 'bg-blue-50 dark:bg-blue-900/50 text-blue-600 dark:text-blue-300' : 'text-gray-700 dark:text-gray-200'}`}
                                      onClick={() => onUpdateUserRole(user.uid, role.value as any)} // Type assertion here might need review later
                                      disabled={user.role === role.value}
                                    >
                                      {role.label}
                                    </DropdownMenuItem>
                                  ))}
                                </>
                              )}
                              {currentUser?.uid !== user.uid && user.role !== 'super_admin' && (
                                 <>
                                  <DropdownMenuSeparator className="dark:bg-gray-700"/>
                                  <DropdownMenuItem
                                    className="cursor-pointer text-red-600 hover:bg-red-50 dark:hover:bg-red-900/50 dark:text-red-400"
                                    onClick={() => {
                                      if (confirm('Are you sure you want to remove this user? This action is irreversible and will delete their account.')) {
                                        // Typically, you'd call a specific delete function. 
                                        // For now, using toggleUserStatus to deactivate and then manually delete if needed.
                                        onToggleUserStatus(user.uid, true); 
                                        // Consider adding a specific onDeleteUser prop if full deletion is intended here
                                      }
                                    }}
                                  >
                                    <UserX className="mr-2 h-4 w-4" /> Remove User
                                  </DropdownMenuItem>
                                 </>
                              )}
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center text-gray-500 dark:text-gray-400">
                  No users found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {totalUsers > 10 && (
        <div className="flex flex-col sm:flex-row justify-between items-center mt-6 gap-4 px-1">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Showing {indexOfFirstUser + 1}-{Math.min(indexOfLastUser, totalUsers)} of {totalUsers} users
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
              disabled={currentPage === 1}
              className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
              className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

export default UsersTable;
