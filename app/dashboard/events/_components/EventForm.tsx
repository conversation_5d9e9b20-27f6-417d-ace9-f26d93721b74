"use client";

import { useState } from "react";
import { Event } from "@/types/events";
import { db } from "@/lib/firebase";
import { doc, setDoc, updateDoc, addDoc, collection } from "firebase/firestore";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const emptyEvent: Event = {
  id: "",
  title: "",
  date: "",
  endDate: "",
  description: "",
  location: "",
  imageUrl: "",
  imageAlt: "",
  eventType: "",
  isCountdownTarget: false,
  isHighlighted: false,
  showRsvp: false,
  order: 0,
  seo: {},
};

import { storage } from "@/lib/firebase";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";

type EventFormProps = {
  event?: Event;
  onSave: () => void;
  onCancel: () => void;
  onDelete?: () => void;
};

export function EventForm({ event, onSave, onCancel, onDelete }: EventFormProps) {
  // Convert Firestore Timestamp to ISO string for datetime-local input
  function toInputDate(val: any) {
    if (!val) return "";
    if (typeof val === "string") return val;
    if (val.seconds) {
      const d = new Date(val.seconds * 1000);
      return d.toISOString().slice(0, 16);
    }
    return "";
  }

  const [form, setForm] = useState<Event>({
    ...emptyEvent,
    ...event,
    date: toInputDate(event?.date),
    endDate: toInputDate(event?.endDate),
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);

  function handleChange(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) {
    const target = e.target;
    const { name, value } = target;
    let newValue: any = value;
    if (target instanceof HTMLInputElement && target.type === "checkbox") {
      newValue = target.checked;
    }
    setForm((prev) => ({
      ...prev,
      [name]: newValue,
    }));
  }

  async function handleImageUpload(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;
    setUploading(true);
    setError(null);
    try {
      const storageRef = ref(storage, `events/${Date.now()}_${file.name}`);
      await uploadBytes(storageRef, file);
      const url = await getDownloadURL(storageRef);
      setForm((prev) => ({ ...prev, imageUrl: url }));
    } catch (err: any) {
      setError("Failed to upload image");
    } finally {
      setUploading(false);
    }
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    setError(null);
    try {
      // Convert date and endDate to Firestore Timestamps
      const { Timestamp } = await import("firebase/firestore");
      const toTimestamp = (val: string | undefined) => val ? Timestamp.fromDate(new Date(val)) : null;
      const payload = {
        ...form,
        date: toTimestamp(form.date ?? ''),
        endDate: toTimestamp(form.endDate ?? ''),
      };
      if (form.id) {
        // Update
        const ref = doc(db, "events", form.id);
        await updateDoc(ref, payload);
      } else {
        // Create
        const ref = await addDoc(collection(db, "events"), payload);
        form.id = ref.id;
        await setDoc(doc(db, "events", form.id), payload);
      }
      onSave();
    } catch (err: any) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("Failed to save event.");
      }
    } finally {
      setLoading(false);
    }
  }

  return (
    <Card className="mb-6 bg-white/90 rounded-2xl shadow-lg border border-gray-200">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-gray-900 drop-shadow">{form.id ? "Edit Event" : "Create Event"}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Title & Location */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">

          {/* --- Separator --- */}
          <hr className="my-6 border-t border-gray-300" />
            <div className="space-y-4">
              <label className="block font-medium text-gray-800">Title</label>
              <input name="title" value={form.title} onChange={handleChange} className="input bg-white text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6C47FF]" required />
            </div>
            <div className="space-y-4">
              <label className="block font-medium text-gray-800">Location</label>
              <input name="location" value={form.location} onChange={handleChange} className="input bg-white text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6C47FF]" />
            </div>
          </div>

          {/* Date & Time */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <label className="block font-medium text-gray-800">Date & Time</label>
              <input name="date" type="datetime-local" value={form.date} onChange={handleChange} className="input bg-white text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6C47FF]" required />
            </div>
            <div className="space-y-4">
              <label className="block font-medium text-gray-800">End Date & Time</label>
              <input name="endDate" type="datetime-local" value={form.endDate ?? ''} onChange={handleChange} className="input bg-white text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6C47FF]" />
            </div>
          </div>

          {/* --- Separator --- */}
          <hr className="my-6 border-t border-gray-300" />

          {/* Description */}
          <div className="space-y-4">
            <label className="block font-medium text-gray-800">Description</label>
            <textarea
              name="description"
              value={form.description ?? ''}
              onChange={handleChange}
              className="input bg-white text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6C47FF] min-h-[100px] p-3 resize-vertical shadow-sm w-full"
              rows={5}
              placeholder="Add a detailed description of the event..."
            />
          </div>

          {/* --- Separator --- */}
          <hr className="my-6 border-t border-gray-300" />

          {/* Type & Order */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <label className="block font-medium text-gray-800">Type</label>
              <input name="eventType" value={form.eventType ?? ''} onChange={handleChange} className="input bg-white text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6C47FF]" />
            </div>
            <div className="space-y-4">
              <label className="block font-medium text-gray-800">Order</label>
              <input name="order" type="number" value={form.order ?? 0} onChange={handleChange} className="input bg-white text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6C47FF]" />
            </div>
          </div>

          {/* --- Separator --- */}
          <hr className="my-6 border-t border-gray-300" />

          {/* Image & Alt */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <label className="block font-medium text-gray-800">Event Image</label>
              <input type="file" accept="image/*" onChange={handleImageUpload} className="input bg-white text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6C47FF]" />
              {form.imageUrl && (
                <img src={form.imageUrl} alt={form.imageAlt ?? 'Event image'} className="mt-2 rounded shadow w-40 h-28 object-cover border border-gray-200" />
              )}
            </div>
            <div className="space-y-4">
              <label className="block font-medium text-gray-800">Image Alt</label>
              <input name="imageAlt" value={form.imageAlt ?? ''} onChange={handleChange} className="input bg-white text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6C47FF]" />
            </div>
            </div>
          {/* --- Separator --- */}
          <hr className="my-6 border-t border-gray-300" />

          {/* Toggles */}
          <div className="flex flex-wrap gap-6 items-center py-2">
            <label className="flex items-center gap-2 text-gray-700">
              <input name="isCountdownTarget" type="checkbox" checked={!!form.isCountdownTarget} onChange={handleChange} /> Countdown Target
            </label>
            <label className="flex items-center gap-2 text-gray-700">
              <input name="isHighlighted" type="checkbox" checked={!!form.isHighlighted} onChange={handleChange} /> Highlighted
            </label>
            <label className="flex items-center gap-2 text-gray-700">
              <input name="showRsvp" type="checkbox" checked={!!form.showRsvp} onChange={handleChange} /> Show RSVP
            </label>
          </div>

          {/* --- Separator --- */}
          <hr className="my-6 border-t border-gray-300" />

          {/* Error & Actions */}
          {error && <div className="text-red-500">{error}</div>}
          <div className="flex gap-2">
            <Button type="submit" disabled={loading}>{loading ? "Saving..." : "Save"}</Button>
            <Button type="button" variant="outline" onClick={onCancel}>Cancel</Button>
            {form.id && onDelete && (
              <Button
                type="button"
                variant="destructive"
                className="ml-auto"
                onClick={async () => {
                  if (window.confirm("Are you sure you want to delete this event? This cannot be undone.")) {
                    try {
                      setLoading(true);
                      setError(null);
                      const { doc, deleteDoc } = await import("firebase/firestore");
                      await deleteDoc(doc(db, "events", form.id));
                      if (onDelete) onDelete();
                    } catch (err: any) {
                      setError("Failed to delete event.");
                    } finally {
                      setLoading(false);
                    }
                  }
                }}
              >
                Delete
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}


