'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { EventForm } from '../../_components/EventForm';
import { db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { Event } from '@/types/events';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2 } from 'lucide-react';
import Link from 'next/link';

export default function EditEventPage() {
  const params = useParams<{ id: string }>();
  const { toast } = useToast();
  const router = useRouter();
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const id = params?.id;

  useEffect(() => {
    async function fetchEvent() {
      if (!id) {
        setError('Event ID is required');
        setLoading(false);
        return;
      }

      try {
        const docRef = doc(db, 'events', id);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          const data = docSnap.data();
          setEvent({
            id: docSnap.id,
            title: data.title || '',
            description: data.description || '',
            date: data.date || '',
            endDate: data.endDate || '',
            location: data.location || '',
            eventType: data.eventType || '',
            imageUrl: data.imageUrl || '',
            imageAlt: data.imageAlt || '',
            isHighlighted: data.isHighlighted || false,
            showRsvp: data.showRsvp || false,
            order: data.order || 0,
            status: data.status || 'draft',
            createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
            updatedAt: data.updatedAt?.toDate?.()?.toISOString() || new Date().toISOString(),
          });
        } else {
          setError('Event not found');
        }
      } catch (err) {
        console.error('Error fetching event:', err);
        setError('Failed to load event');
      } finally {
        setLoading(false);
      }
    }

    fetchEvent();
  }, [id]);

  const handleSave = async () => {
    try {
      toast({
        title: 'Success',
        description: 'Event updated successfully',
      });
      router.push('/dashboard/events');
    } catch (error) {
      console.error('Error updating event:', error);
      toast({
        title: 'Error',
        description: 'Failed to update event',
        variant: 'destructive',
      });
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const handleDelete = async () => {
    try {
      toast({
        title: 'Success',
        description: 'Event deleted successfully',
      });
      router.push('/dashboard/events');
    } catch (error) {
      console.error('Error deleting event:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete event',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <p className="text-destructive">{error}</p>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="container mx-auto py-6">
        <p>Event not found</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="icon"
            asChild
          >
            <Link href="/dashboard/events">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Edit Event</h1>
            <p className="text-sm text-muted-foreground">
              Update the event details below
            </p>
          </div>
        </div>
      </div>

      <EventForm
        event={event}
        onSave={handleSave}
        onCancel={handleCancel}
        onDelete={handleDelete}
      />
    </div>
  );
}
