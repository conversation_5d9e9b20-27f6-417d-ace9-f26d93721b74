'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Plus, Loader2, RefreshCw } from 'lucide-react';
import { collection, getDocs, deleteDoc, doc, updateDoc, query, orderBy, serverTimestamp } from 'firebase/firestore';
import { ref, listAll, deleteObject } from 'firebase/storage';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { EventsTable } from './_components/EventsTable';
import { Event } from '@/types/events';
import { db, storage } from '@/lib/firebase';

export default function EventsPage() {
  const { toast } = useToast();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [updatingId, setUpdatingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    setLoading(true);
    setError(null);
    try {
      const q = query(collection(db, 'events'), orderBy('date', 'desc'));
      const snapshot = await getDocs(q);
      const items: Event[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        items.push({
          id: doc.id,
          title: data.title || '',
          description: data.description || '',
          date: data.date || '',
          endDate: data.endDate || '',
          location: data.location || '',
          imageUrl: data.imageUrl || data.image || '',
          imageAlt: data.imageAlt || '',
          eventType: data.eventType || 'event',
          isCountdownTarget: data.isCountdownTarget || false,
          isHighlighted: data.isHighlighted || false,
          showRsvp: data.showRsvp || false,
          order: data.order || 0,
          status: data.status || 'draft',
          createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || new Date().toISOString(),
        });
      });
      
      setEvents(items);
    } catch (err) {
      console.error('Error fetching events:', err);
      setError('Failed to load events. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateEvent = async (id: string, updates: Partial<Event>) => {
    setUpdatingId(id);
    try {
      await updateDoc(doc(db, 'events', id), {
        ...updates,
        updatedAt: serverTimestamp(),
      });
      
      setEvents(prev => 
        prev.map(event => 
          event.id === id ? { ...event, ...updates } : event
        )
      );
      
      toast({
        title: 'Success',
        description: 'Event updated successfully',
      });
    } catch (error) {
      console.error('Error updating event:', error);
      toast({
        title: 'Error',
        description: 'Failed to update event',
        variant: 'destructive',
      });
    } finally {
      setUpdatingId(null);
    }
  };



  const handleDeleteEvent = async (id: string) => {
    setDeletingId(id);
    try {
      await deleteDoc(doc(db, 'events', id));
      
      // Delete associated images from storage
      const storageRef = ref(storage, `events/${id}`);
      const files = await listAll(storageRef);
      
      await Promise.all(files.items.map(file => 
        deleteObject(file).catch(console.error)
      ));
      
      setEvents(prev => prev.filter(event => event.id !== id));
      
      // Don't show toast here - let the DeleteConfirm component handle it
      return true;
    } catch (error) {
      console.error('Error deleting event:', error);
      throw new Error('Failed to delete event');
    } finally {
      setDeletingId(null);
    }
  };

  const handleSyncEvents = async () => {
    setSyncing(true);
    try {
      // Add your sync logic here
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate sync
      toast({
        title: 'Sync complete',
        description: 'Events have been synced successfully.',
      });
    } catch (error) {
      console.error('Error syncing events:', error);
      toast({
        title: 'Error syncing events',
        description: 'There was an error syncing events. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  if (loading && events.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Events</h1>
            <p className="text-sm text-muted-foreground">
              Manage all events and their details
            </p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleSyncEvents}
              disabled={syncing}
              className="w-full sm:w-auto"
            >
              {syncing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Sync Events
                </>
              )}
            </Button>
            <Button asChild className="w-full sm:w-auto">
              <Link href="/dashboard/events/new">
                <Plus className="mr-2 h-4 w-4" />
                Add Event
              </Link>
            </Button>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Manage your events. Click "Sync Images" to import new images from storage.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Upcoming Events</CardTitle>
          <CardDescription>
            Manage events and their visibility on your website
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          ) : (
            <EventsTable 
              data={events} 
              onDelete={handleDeleteEvent}
              onUpdate={handleUpdateEvent}
              loading={loading}
              updatingId={updatingId}
              deletingId={deletingId}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
