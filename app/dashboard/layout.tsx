'use client'

import { Sidebar } from '@/components/sidebar'
import { Header } from '@/components/header'
import { Toaster } from '@/components/ui/toaster'
import { usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useSidebar } from '@/hooks/use-sidebar'
import { cn } from '@/lib/utils'

const getPageTitle = (pathname: string) => {
  const path = pathname.split('/').pop() || 'Dashboard'
  return path.charAt(0).toUpperCase() + path.slice(1)
}

import { SidebarProvider } from '@/components/ui/sidebar'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const { isOpen, setOpen, isCollapsed } = useSidebar()
  const [pageTitle, setPageTitle] = useState('Dashboard')
  const [isClient, setIsClient] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // Handle mobile state
  useEffect(() => {
    const checkIfMobile = () => {
      const isMobileView = window.innerWidth < 768
      setIsMobile(isMobileView)
      // On mobile, sidebar should be closed by default
      // On desktop, sidebar should be open by default
      if (isMobileView) {
        setOpen(false)
      } else {
        setOpen(true)
      }
    }

    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [setOpen])

  useEffect(() => {
    setIsClient(true)
    if (pathname) {
      setPageTitle(getPageTitle(pathname))
    }
  }, [pathname])

  if (!isClient) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-50">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <SidebarProvider>
      {/* Mobile overlay */}
      {isOpen && isMobile && (
        <div
          className="sidebar-overlay md:hidden"
          onClick={() => setOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'sidebar-fixed bg-white shadow-lg transition-all duration-300 ease-in-out h-screen',
          isCollapsed ? 'w-16' : 'w-64',
          isMobile && (isOpen ? 'absolute left-0 top-0 z-50 translate-x-0' : 'absolute -translate-x-full'),
          !isMobile && 'fixed left-0 top-0 z-30',
        )}
        style={{ transitionProperty: 'width, left, transform' }}
      >
        <Sidebar />
      </div>

      {/* Main content wrapper */}
      <div
        className={cn(
          'flex flex-col min-h-screen transition-all duration-300 ease-in-out pt-16 bg-gray-50 flex-1 w-full',
          !isMobile && (isCollapsed ? 'ml-16' : 'ml-64'),
          isMobile && 'ml-0'
        )}
      >
        {/* Header */}
        <Header />

        {/* Main content */}
        <main className="main-content flex-1 w-full bg-gray-50 p-4 sm:p-6">
          <div className="w-full">
            {children}
          </div>
        </main>
      </div>

      <Toaster />
    </SidebarProvider>
  )
}
