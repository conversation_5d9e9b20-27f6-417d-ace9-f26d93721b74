"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { BarChart3, Newspaper, Users, Calendar, Image as ImageIcon, ArrowRight, Activity, TrendingUp, FileText, Crown } from 'lucide-react'
import Image from 'next/image'
import { useEffect, useState } from 'react'
// Import Firebase auth
import { auth, db } from '@/lib/firebase'
import { collection, getDocs, query, where, getCountFromServer } from 'firebase/firestore'
import { LOGO_URL } from '@/lib/storage'

interface User {
  displayName: string | null;
  email: string | null;
  // Add other user properties you need
}

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalNews: 0,
    activeUsers: 0,
    events: 0,
    galleryItems: 0,
    activeSessions: 0,
    pageViews: 0,
    avgSessionDuration: '0m 0s'
  })

  // Fetch dashboard data from Firestore
  const fetchDashboardData = async () => {
    if (!auth.currentUser) return
    
    try {
      // Get news count
      const newsQuery = query(collection(db, 'news'))
      const newsSnapshot = await getCountFromServer(newsQuery)
      
      // Get users count (you might want to adjust this query based on your users collection structure)
      const usersQuery = query(collection(db, 'users'), where('status', '==', 'active'))
      const usersSnapshot = await getCountFromServer(usersQuery)
      
      // Get events count
      const eventsQuery = query(collection(db, 'events'))
      const eventsSnapshot = await getCountFromServer(eventsQuery)
      
      // Get gallery items count
      const galleryQuery = query(collection(db, 'gallery'))
      const gallerySnapshot = await getCountFromServer(galleryQuery)
      
      // Get analytics data (you'll need to implement analytics tracking separately)
      const analyticsData = {
        activeSessions: 0,
        pageViews: 0,
        avgSessionDuration: '0m 0s'
      }
      
      setStats({
        totalNews: newsSnapshot.data().count,
        activeUsers: usersSnapshot.data().count,
        events: eventsSnapshot.data().count,
        galleryItems: gallerySnapshot.data().count,
        ...analyticsData
      })
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    }
  }

  useEffect(() => {
    // Check if auth is available
    if (!auth) {
      console.error('Firebase auth not initialized')
      setLoading(false)
      return
    }

    try {
      const unsubscribe = auth.onAuthStateChanged(async (user) => {
        if (user) {
          setUser({
            displayName: user.displayName || 'Admin',
            email: user.email,
          })
          // Fetch dashboard data after auth is confirmed
          await fetchDashboardData()
        } else {
          setUser(null)
          // Optionally redirect to login if not authenticated
          // router.push('/login')
        }
        setLoading(false)
      })

      // Cleanup subscription on unmount
      return () => unsubscribe()
    } catch (error) {
      console.error('Error setting up auth listener:', error)
      setLoading(false)
    }
  }, [])

  // Format numbers with commas
  const formatNumber = (num: number) => num.toLocaleString()

  // Calculate percentage change
  const getChange = (current: number, previous: number) => {
    if (previous === 0) return '+0%'
    const change = ((current - previous) / previous) * 100
    return `${change >= 0 ? '+' : ''}${change.toFixed(1)}%`
  }

  // Get the first name or fallback to a generic greeting
  const displayName = user?.displayName || 'Your Majesty'
  
  // Stats with real data
  const statsData = [
    { 
      name: 'Total News', 
      value: formatNumber(stats.totalNews), 
      change: getChange(stats.totalNews, Math.floor(stats.totalNews * 0.9)), // Mocking previous data
      icon: <Newspaper className="h-6 w-6" /> 
    },
    { 
      name: 'Active Users', 
      value: formatNumber(stats.activeUsers), 
      change: getChange(stats.activeUsers, Math.floor(stats.activeUsers * 0.95)),
      icon: <Users className="h-6 w-6" /> 
    },
    { 
      name: 'Events', 
      value: formatNumber(stats.events), 
      change: getChange(stats.events, Math.floor(stats.events * 0.9)),
      icon: <Calendar className="h-6 w-6" /> 
    },
    { 
      name: 'Gallery Items', 
      value: formatNumber(stats.galleryItems), 
      change: getChange(stats.galleryItems, Math.floor(stats.galleryItems * 0.8)),
      icon: <ImageIcon className="h-6 w-6" /> 
    },
  ]

  const recentActivity = [
    { id: 1, user: 'John Doe', action: 'created a new article', time: '5 min ago', icon: <FileText className="h-4 w-4 text-royalBlue-500" /> },
    { id: 2, user: 'Jane Smith', action: 'updated the events calendar', time: '1 hour ago', icon: <Calendar className="h-4 w-4 text-forestGreen-500" /> },
    { id: 3, user: 'Mike Johnson', action: 'uploaded new images', time: '2 hours ago', icon: <ImageIcon className="h-4 w-4 text-royalGold-500" /> },
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Banner */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-royalBlue-600 to-royalBlue-800 p-4 sm:p-6 text-white shadow">
        <div className="absolute inset-0 bg-[url('/images/pattern.svg')] opacity-10"></div>
        
        <div className="relative z-10 flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold tracking-tight">
              Welcome back, <span className="text-royalGold-300">
                {loading ? '...' : displayName.split(' ')[0]}
              </span>
            </h1>
            <p className="mt-2 text-royalBlue-100">Here's what's happening with your kingdom today</p>
          </div>
          <div className="hidden md:block">
            <Image 
              src="/images/ai-logo1a.png"
              alt="Adukrom Kingdom Logo" 
              width={140}
              height={70}
              className="rounded-lg"
              priority
            />
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {statsData.map((stat) => (
          <div 
            key={stat.name}
            className="relative overflow-hidden rounded-xl bg-white p-4 shadow-sm transition-all duration-200 hover:shadow"
          >
            <div className="absolute right-4 top-4 rounded-full bg-gradient-to-br from-royalBlue-100 to-royalBlue-50 p-3 text-royalBlue-600">
              {stat.icon}
            </div>
            <h3 className="text-sm font-medium text-gray-500">{stat.name}</h3>
            <p className="mt-1 text-2xl font-semibold text-gray-900">{stat.value}</p>
            <p className="mt-2 flex items-center text-sm text-green-600">
              <TrendingUp className="mr-1 h-4 w-4" />
              {stat.change} from last month
            </p>
          </div>
        ))}
      </div>

      <div className="grid gap-4 lg:grid-cols-3">
        {/* Quick Stats */}
        <Card className="bg-white border shadow-sm h-full">
          <div className="absolute inset-0 bg-gradient-to-br from-royalBlue-50/50 to-ivory-50/50 -z-10"></div>
          <CardHeader>
            <CardTitle className="flex items-center text-gray-800">
              <Activity className="mr-2 h-5 w-5 text-royalBlue-600" />
              Quick Stats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: 'Active Sessions', value: formatNumber(stats.activeSessions), change: getChange(stats.activeSessions, Math.floor(stats.activeSessions * 0.9)) },
                { name: 'Page Views', value: formatNumber(stats.pageViews), change: getChange(stats.pageViews, Math.floor(stats.pageViews * 0.95)) },
                { name: 'Avg. Session', value: stats.avgSessionDuration, change: '+0%' },
              ].map((stat, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <div>
                    <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                    <p className="text-lg font-semibold text-gray-900">{stat.value}</p>
                  </div>
                  <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                    {stat.change}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="lg:col-span-2 bg-white border shadow-sm h-full">
          <div className="absolute inset-0 bg-gradient-to-br from-ivory-50/50 to-forestGreen-50/50 -z-10"></div>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center text-gray-800">
                <BarChart3 className="mr-2 h-5 w-5 text-forestGreen-600" />
                Recent Activity
              </CardTitle>
              <Button variant="ghost" size="sm" className="text-royalBlue-600 hover:bg-royalBlue-50">
                View All
                <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start">
                  <div className="mr-3 mt-0.5 flex h-8 w-8 items-center justify-center rounded-full bg-royalBlue-100 text-royalBlue-600">
                    {activity.icon}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      <span className="font-semibold text-royalBlue-600">{activity.user}</span> {activity.action}
                    </p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="rounded-xl bg-white border p-4 sm:p-6 shadow-sm">
        <h2 className="mb-6 text-xl font-semibold text-gray-800">Quick Actions</h2>
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {[
            { name: 'Create Post', icon: <FileText className="h-5 w-5" />, color: 'bg-royalBlue-100 text-royalBlue-600' },
            { name: 'Add Event', icon: <Calendar className="h-5 w-5" />, color: 'bg-forestGreen-100 text-forestGreen-600' },
            { name: 'Upload Media', icon: <ImageIcon className="h-5 w-5" />, color: 'bg-royalGold-100 text-royalGold-600' },
            { name: 'View Analytics', icon: <BarChart3 className="h-5 w-5" />, color: 'bg-purple-100 text-purple-600' },
          ].map((action, index) => (
            <button
              key={index}
              className={`flex items-center justify-center space-x-2 rounded-lg p-3 text-left transition-all hover:shadow ${action.color} hover:bg-opacity-80`}
            >
              <div className="rounded-lg bg-white/30 p-2 backdrop-blur-sm">
                {action.icon}
              </div>
              <span className="font-medium">{action.name}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}