'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { Plus, Loader2, RefreshCw } from 'lucide-react';
import { ref, getDownloadURL, deleteObject, StorageReference } from 'firebase/storage';
import { collection, getDocs, query, orderBy, doc, updateDoc, serverTimestamp, deleteDoc } from 'firebase/firestore';
import { syncStorageWithFirestore } from '@/lib/sync-utils';
import { db, storage } from '@/lib/firebase';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { GalleryTable } from './_components/gallery-table';
import { GalleryItem } from '@/types/gallery';


const GalleryPage = () => {
  // State
  const { toast } = useToast();
  const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [updatingId, setUpdatingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const [lastSync, setLastSync] = useState<Date | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  
  // Track mounted state to prevent memory leaks
  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Helper function to safely parse dates from Firestore
  const parseDate = (value: any): string => {
    if (!value) return new Date().toISOString();
    if (value.toDate) return value.toDate().toISOString();
    if (typeof value === 'string' || typeof value === 'number') return new Date(value).toISOString();
    return new Date().toISOString();
  };

  // Get image URL from storage path
  const getImageUrl = async (storagePath: string): Promise<string> => {
    if (!storagePath) return '';
    try {
      // Ensure the path is in the correct format
      const fullPath = storagePath.startsWith('Website Images/') || storagePath.startsWith('Website%20Images/')
        ? storagePath
        : `Website Images/${storagePath}`;
      
      // Remove any URL encoding as Firebase Storage handles spaces in paths
      const cleanPath = fullPath.replace(/%20/g, ' ');
      const imageRef = ref(storage, cleanPath);
      
      console.log('Getting URL for path:', cleanPath);
      const url = await getDownloadURL(imageRef);
      console.log('Got URL:', url);
      return url;
    } catch (error) {
      console.error('Error getting image URL for path', storagePath, ':', error);
      return '';
    }
  };

  // Fetch gallery items from Firestore
  const fetchGalleryItems = useCallback(async () => {
    try {
      const q = query(collection(db, 'gallery'), orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);
      
      const items: GalleryItem[] = [];
      
      for (const doc of snapshot.docs) {
        try {
          const data = doc.data();
          
          // Skip if required fields are missing or invalid
          if (!data || typeof data !== 'object' || 
              typeof data.title !== 'string' || 
              !data.imageUrl) {
            console.warn(`Skipping gallery item ${doc.id}: Invalid data structure`);
            continue;
          }
          
          // Get URLs for images
          const imageUrl = data.storagePath ? await getImageUrl(data.storagePath) : data.imageUrl;
          const thumbnailUrl = data.thumbnailStoragePath ? 
            await getImageUrl(data.thumbnailStoragePath) : data.thumbnailUrl || '';
          
          const galleryItem: GalleryItem = {
            id: doc.id,
            title: data.title,
            description: data.description || '',
            imageUrl,
            storagePath: data.storagePath || '',
            thumbnailUrl,
            thumbnailStoragePath: data.thumbnailStoragePath || '',
            featured: data.featured || false,
            status: data.status || 'draft',
            order: data.order || 0,
            tags: data.tags || [],
            createdAt: parseDate(data.createdAt),
            updatedAt: parseDate(data.updatedAt),
          };
          
          items.push(galleryItem);
          
        } catch (error) {
          console.error(`Error processing gallery item ${doc.id}:`, error);
        }
      }
      
      setGalleryItems(items);
      return items;
    } catch (error) {
      console.error('Error fetching gallery items:', error);
      setError('Failed to load gallery items');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Sync with Firebase Storage
  const syncWithStorage = useCallback(async () => {
    if (!isMounted) return;
    
    console.log('=== Starting sync with storage ===');
    setSyncing(true);
    setError(null);
    
    try {
      // Log storage configuration for debugging
      console.log('Storage configuration:', {
        appId: storage.app.options.appId,
        projectId: storage.app.options.projectId,
        storageBucket: storage.app.options.storageBucket,
      });
      
      // Try different path variations
      const pathVariations = [
        'Website Images',
        'Website%20Images',
        'website-images',
        'gallery',
        ''
      ];
      
      let syncSuccessful = false;
      let lastError: Error | null = null;
      
      for (const path of pathVariations) {
        console.log(`\n=== Trying path: '${path}' ===`);
        try {
          await syncStorageWithFirestore(path, 'gallery', {
            processItem: async (itemRef: StorageReference, url: string, metadata: any) => {
              const storagePath = itemRef.fullPath;
              const defaultTitle = itemRef.name
                .replace(/\.[^/.]+$/, '')
                .replace(/[-_]/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase());
              
              return {
                title: metadata.customMetadata?.title || defaultTitle,
                description: metadata.customMetadata?.description || '',
                storagePath,
                imageUrl: url,
                status: 'published',
                featured: false,
                order: 0,
                tags: metadata.customMetadata?.tags?.split(',').map((t: string) => t.trim()) || [],
                createdAt: serverTimestamp(),
                updatedAt: serverTimestamp()
              };
            },
            transformExisting: (existingDoc: any, itemRef: StorageReference, metadata: any) => {
              if (existingDoc.storagePath === itemRef.fullPath) {
                return {
                  ...existingDoc,
                  title: metadata.customMetadata?.title || existingDoc.title,
                  description: metadata.customMetadata?.description || existingDoc.description,
                  updatedAt: serverTimestamp()
                };
              }
              return null;
            }
          });
          
          syncSuccessful = true;
          console.log(`Successfully synced with path: ${path}`);
          break;
          
        } catch (error) {
          console.error(`Error syncing with path '${path}':`, error);
          lastError = error instanceof Error ? error : new Error(String(error));
        }
      }
      
      if (!syncSuccessful) {
        throw lastError || new Error('Failed to sync with any path variation');
      }
      
      // Force a fresh fetch after successful sync
      const updatedItems = await fetchGalleryItems();
      if (isMounted) {
        setGalleryItems(updatedItems);
        setLastSync(new Date());
      }
      
      toast({
        title: 'Sync successful',
        description: 'Gallery has been synced with storage',
      });
      
    } catch (syncError) {
      console.error('Error during sync:', syncError);
      if (isMounted) {
        setError('Failed to sync with storage. Please try again.');
        toast({
          title: 'Sync failed',
          description: 'Could not sync with storage',
          variant: 'destructive',
        });
      }
    } finally {
      if (isMounted) {
        setSyncing(false);
      }
    }
  }, [fetchGalleryItems, isMounted, toast]);

  // Load gallery items on mount
  useEffect(() => {
    fetchGalleryItems();
  }, [fetchGalleryItems]);

  // Handle sync button click
  const handleSync = async () => {
    setSyncing(true);
    try {
      await syncWithStorage();
      toast({
        title: 'Success',
        description: 'Gallery synced with storage successfully',
      });
    } catch (error) {
      console.error('Error syncing gallery:', error);
      toast({
        title: 'Error',
        description: 'Failed to sync gallery with storage',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  // Handle gallery item update
  const handleUpdate = async (id: string, updates: Partial<Omit<GalleryItem, 'id' | 'createdAt' | 'updatedAt'>>) => {
    setUpdatingId(id);
    try {
      const galleryRef = doc(db, 'gallery', id);
      await updateDoc(galleryRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });
      
      setGalleryItems(prevItems => 
        prevItems.map(item => 
          item.id === id ? { 
            ...item, 
            ...updates, 
            updatedAt: new Date().toISOString() 
          } : item
        )
      );
      
      toast({
        title: 'Success',
        description: 'Gallery item updated successfully',
      });
    } catch (error) {
      console.error('Error updating gallery item:', error);
      toast({
        title: 'Error',
        description: 'Failed to update gallery item',
        variant: 'destructive',
      });
    } finally {
      setUpdatingId(null);
    }
  };

  // Handle delete gallery item
  const handleDeleteItem = async (id: string): Promise<boolean> => {
    try {
      setDeletingId(id);
      
      // Delete from Firestore
      await deleteDoc(doc(db, 'gallery', id));
      
      // Delete associated images from storage if they exist
      const item = galleryItems.find(item => item.id === id);
      if (item?.storagePath) {
        try {
          const imageRef = ref(storage, item.storagePath);
          await deleteObject(imageRef);
          
          if (item.thumbnailStoragePath) {
            const thumbRef = ref(storage, item.thumbnailStoragePath);
            await deleteObject(thumbRef);
          }
        } catch (error) {
          console.error('Error deleting image from storage:', error);
          // Continue with deletion even if storage delete fails
        }
      }
      
      // Update local state
      setGalleryItems(prev => prev.filter(item => item.id !== id));
      
      return true;
    } catch (error) {
      console.error('Error deleting gallery item:', error);
      return false;
    } finally {
      setDeletingId(null);
    }
  };

  // Handle window focus to refresh data
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchGalleryItems();
      }
    };

    window.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', fetchGalleryItems);

    return () => {
      window.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', fetchGalleryItems);
    };
  }, [fetchGalleryItems]);



  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gallery</h1>
          {lastSync && (
            <p className="text-sm text-muted-foreground">
              Last synced: {new Date(lastSync).toLocaleString()}
            </p>
          )}
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="default"
              onClick={handleSync}
              disabled={loading || syncing}
              className="min-w-[120px]"
            >
              {syncing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Sync with Storage
                </>
              )}
            </Button>
            {error && (
              <p className="text-sm text-destructive">
                {error}
              </p>
            )}
          </div>
          <Button asChild>
            <Link href="/dashboard/gallery/new" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add New
            </Link>
          </Button>
        </div>
      </div>

      <div className="rounded-lg border bg-card">
        <div className={`relative ${loading ? 'opacity-50 pointer-events-none' : ''}`}>
          <GalleryTable 
            data={galleryItems} 
            onDelete={handleDeleteItem} 
            onUpdate={handleUpdate}
            loading={loading}
            updatingId={updatingId}
            deletingId={deletingId}
          />
        </div>
      </div>
    </div>
  );
};

export default GalleryPage;
