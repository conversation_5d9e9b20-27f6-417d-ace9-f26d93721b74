'use client';

import Link from 'next/link';
import { format } from 'date-fns';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Image as ImageIcon, Loader2 } from 'lucide-react';
import { ActionButtons } from '@/components/shared/actions/action-buttons';
import { GalleryItem } from '@/types/gallery';
import Image from 'next/image';

import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface GalleryTableProps {
  data: GalleryItem[];
  onDelete: (id: string) => Promise<boolean>;
  onUpdate: (id: string, updates: Partial<GalleryItem>) => Promise<void>;
  loading?: boolean;
  deletingId?: string | null;
  updatingId?: string | null;
}

export function GalleryTable({ 
  data, 
  onDelete, 
  onUpdate,
  loading = false, 
  deletingId = null,
  updatingId = null 
}: GalleryTableProps) {
  
  const handleStatusChange = async (id: string, newStatus: 'draft' | 'published') => {
    await onUpdate(id, { status: newStatus });
  };

  const handleFeaturedChange = async (id: string, isFeatured: boolean) => {
    await onUpdate(id, { featured: isFeatured });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-royalBlue-500"></div>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader className="bg-muted/50">
          <TableRow>
            <TableHead>Image</TableHead>
            <TableHead>Title</TableHead>
            <TableHead>Featured</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length > 0 ? (
            data.map((item) => (
              <TableRow key={item.id} className="hover:bg-muted/20">
                <TableCell className="w-[100px] p-4">
                  <div className="h-16 w-16 relative rounded-md overflow-hidden">
                    {item.imageUrl ? (
                      <Image
                        src={item.imageUrl}
                        alt={item.title}
                        fill
                        className="object-cover"
                        sizes="(max-width: 64px) 100vw, 64px"
                      />
                    ) : (
                      <div className="h-full w-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                        <ImageIcon className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell className="font-medium">
                  {item.title}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium ${!item.featured ? 'text-muted-foreground' : ''}`}>
                      No
                    </span>
                    <div className="relative flex items-center">
                      <div className="relative w-16 h-8 rounded-full overflow-hidden">
                        <div className={`absolute inset-0 transition-colors ${item.featured ? 'bg-green-500' : 'bg-red-500'}`} />
                        <div 
                          className={`absolute top-1 w-6 h-6 bg-white rounded-full shadow-md transform transition-transform ${item.featured ? 'translate-x-9' : 'translate-x-1'}`}
                        />
                        <Switch
                          checked={item.featured}
                          onCheckedChange={(checked) => handleFeaturedChange(item.id, checked)}
                          disabled={updatingId === item.id}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                      </div>
                    </div>
                    <span className={`text-sm font-medium ${item.featured ? 'text-green-600' : 'text-muted-foreground'}`}>
                      Yes
                    </span>
                    {updatingId === item.id && (
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Select
                      value={item.status}
                      onValueChange={(value: 'draft' | 'published') => handleStatusChange(item.id, value)}
                      disabled={updatingId === item.id}
                    >
                      <SelectTrigger className={`w-[120px] font-medium ${item.status === 'published' ? 'bg-green-100 border-green-300 text-green-800' : 'bg-amber-100 border-amber-200 text-amber-800'}`}>
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft" className="focus:bg-amber-50">Draft</SelectItem>
                        <SelectItem value="published" className="focus:bg-green-50">Published</SelectItem>
                      </SelectContent>
                    </Select>
                    {updatingId === item.id && (
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {format(new Date(item.createdAt), 'MMM d, yyyy')}
                </TableCell>
                <TableCell className="p-4 whitespace-nowrap text-right">
                  <div className="flex items-center justify-end gap-2">
                    <div className="flex items-center">
                      <ActionButtons
                        editHref={`/dashboard/gallery/${item.id}/edit`}
                        onDelete={onDelete}
                        deleteId={item.id}
                        deleteTitle={`Delete ${item.title}`}
                        deleteDescription="Are you sure you want to delete this gallery item? This action cannot be undone."
                        editButtonProps={{
                          variant: 'ghost',
                          size: 'icon',
                          className: 'h-8 w-8',
                          disabled: updatingId === item.id || deletingId === item.id
                        }}
                        deleteButtonProps={{
                          variant: 'ghost',
                          size: 'icon',
                          className: 'h-8 w-8 text-destructive hover:text-destructive',
                          disabled: updatingId === item.id || deletingId === item.id
                        }}
                      />
                    </div>
                    {(updatingId === item.id || deletingId === item.id) && (
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={5} className="h-24 text-center">
                No gallery items found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
