'use client';

import { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Loader2, Upload, X } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { ref, uploadBytesResumable, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage, db } from '@/lib/firebase';

import { useToast } from '@/hooks/use-toast';
import { serverTimestamp, doc, updateDoc } from 'firebase/firestore';

import { GalleryItem } from '@/types/gallery';

// Type for uploaded images
interface UploadedImage {
  url: string;
  storagePath: string;
  thumbnailUrl?: string;
  thumbnailStoragePath?: string;
  name: string;
  size: number;
  type: string;
}

// Type for form values
type FormValues = {
  title: string;
  description: string;
  status: 'draft' | 'published';
  featured: boolean;
  images: File[];
};

// Define the form schema with proper types
const formSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().default(''),
  images: z.any(),
  status: z.enum(['draft', 'published']).default('draft'),
  featured: z.boolean().default(false)
});

// Define the props interface
interface GalleryFormProps {
  initialData?: GalleryItem | null;
  onSubmit?: (data: Omit<GalleryItem, 'id'>) => Promise<void>;
  onSuccess?: () => void;
  loading?: boolean;
}

export function GalleryForm({ initialData, onSubmit, onSuccess, loading = false }: GalleryFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewUrls, setPreviewUrls] = useState<Array<{ url: string; file: File }>>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [currentUploadIndex, setCurrentUploadIndex] = useState(0);
  const [totalUploads, setTotalUploads] = useState(0);
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>(() => {
    if (!initialData?.images) return [];
    
    return initialData.images.map(img => ({
      url: img.url,
      storagePath: img.storagePath,
      thumbnailUrl: img.thumbnailUrl,
      thumbnailStoragePath: img.thumbnailStoragePath,
      name: img.name || img.storagePath?.split('/').pop() || 'image',
      size: img.size || 0,
      type: img.type || 'image/jpeg'
    }));
  });

  // Initialize form with proper types
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema) as any, // Type assertion to handle zod resolver type mismatch
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      status: initialData?.status || 'draft',
      featured: initialData?.featured || false,
      images: [],
    },
  });

  // Helper to convert FileList to File[]
  const fileListToArray = (files: FileList | File[] | null | undefined): File[] => {
    if (!files) return [];
    return files instanceof FileList ? Array.from(files) : files;
  };

  // Helper to handle file input change
  const handleFileInputChange = (files: FileList | null) => {
    if (!files?.length) return;
    
    const fileArray = fileListToArray(files);
    const imageFiles = fileArray.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length === 0) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload only image files',
        variant: 'destructive',
      });
      return;
    }
    
    // Create preview URLs for the new files
    const newPreviewUrls = imageFiles.map(file => ({
      url: URL.createObjectURL(file),
      file
    }));
    
    // Update preview URLs state
    setPreviewUrls(prev => [...prev, ...newPreviewUrls]);
    
    // Get current files from form and add new ones
    const currentFiles = form.getValues('images') || [];
    const newFiles = [...currentFiles, ...imageFiles];
    
    // Update form field with combined files
    form.setValue('images', newFiles, { shouldValidate: true });
  };



  // Initialize form with existing data when editing
  useEffect(() => {
    if (initialData) {
      form.reset({
        title: initialData.title,
        description: initialData.description || '',
        status: initialData.status,
        featured: initialData.featured || false,
        images: [],
      });
      setUploadedImages(initialData.images || []);
    }
  }, [initialData, form]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // Create preview URLs for all selected files
    const newPreviewUrls = files.map(file => ({
      url: URL.createObjectURL(file),
      file
    }));

    setPreviewUrls(prev => [...prev, ...newPreviewUrls]);
    form.setValue('images', [...(form.getValues('images') || []), ...files]);
  };

  const removeImage = (index: number) => {
    const currentImages = form.getValues('images');
    const filesArray = currentImages instanceof FileList 
      ? Array.from(currentImages) 
      : [...currentImages];
    
    filesArray.splice(index, 1);
    form.setValue('images', filesArray);

    const newPreviewUrls = [...previewUrls];
    newPreviewUrls.splice(index, 1);
    setPreviewUrls(newPreviewUrls);
  };

  const removeUploadedImage = async (index: number) => {
    if (!initialData?.id) return;
    
    try {
      const imageToDelete = uploadedImages[index];
      // Delete from storage
      const imageRef = ref(storage, imageToDelete.storagePath);
      await deleteObject(imageRef);
      
      // Update state
      const newUploadedImages = [...uploadedImages];
      newUploadedImages.splice(index, 1);
      setUploadedImages(newUploadedImages);
      
      // Update Firestore if editing existing item
      if (initialData.id) {
        await updateDoc(doc(db, 'gallery', initialData.id), {
          images: newUploadedImages,
          updatedAt: serverTimestamp()
        });
      }
      
      toast({
        title: 'Success',
        description: 'Image removed successfully',
      });
    } catch (error) {
      console.error('Error removing image:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove image',
        variant: 'destructive',
      });
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    handleFileInputChange(e.dataTransfer.files);
  };

  const uploadFile = (file: File, index: number): Promise<UploadedImage> => {
    return new Promise((resolve, reject) => {
      const storageRef = ref(storage, `gallery/${file.name}-${Date.now()}`);
      const uploadTask = uploadBytesResumable(storageRef, file);

      uploadTask.on(
        'state_changed',
        (snapshot) => {
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          setUploadProgress(progress);
          setCurrentUploadIndex(index);
        },
        (error) => {
          console.error('Upload failed:', error);
          reject(error);
        },
        async () => {
          const url = await getDownloadURL(uploadTask.snapshot.ref);
          const storagePath = uploadTask.snapshot.ref.fullPath;
          resolve({
            url,
            storagePath,
            thumbnailUrl: url, // Use the same URL as thumbnail for now
            thumbnailStoragePath: storagePath, // Use the same path for thumbnail for now
            name: file.name,
            size: file.size,
            type: file.type
          });
        }
      );
    });
  };

  const handleFormSubmit = async (values: FormValues) => {
    if (!onSubmit) {
      toast({
        title: 'Error',
        description: 'No submit handler provided',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(0);
      
      // Convert FileList to File[] if needed
      const fileArray = fileListToArray(values.images);
      
      // Check if we have any images to process
      if (fileArray.length === 0 && (!uploadedImages || uploadedImages.length === 0)) {
        throw new Error('Please add at least one image');
      }
      
      setTotalUploads(fileArray.length);
      
      // Upload new files
      const uploadedFiles: UploadedImage[] = [];
      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i];
        try {
          const result = await uploadFile(file, i);
          uploadedFiles.push(result);
        } catch (error) {
          console.error(`Failed to upload file ${file.name}:`, error);
          // Continue with other files even if one fails
        }
      }

      // Combine existing images with newly uploaded ones
      const allImages = [
        ...(uploadedImages || []),
        ...uploadedFiles
      ].filter((img): img is UploadedImage => img !== null && img !== undefined);

      if (allImages.length === 0) {
        throw new Error('No valid images to upload');
      }

      // Create a new gallery item object with all required properties
      const galleryItem: Omit<GalleryItem, 'id'> = {
        title: values.title,
        description: values.description || '',
        imageUrl: allImages[0]?.url || '',
        storagePath: allImages[0]?.storagePath || '',
        thumbnailUrl: allImages[0]?.thumbnailUrl || allImages[0]?.url || '',
        thumbnailStoragePath: allImages[0]?.thumbnailStoragePath || allImages[0]?.storagePath || '',
        featured: values.featured,
        status: values.status,
        createdAt: initialData?.createdAt ? 
          (typeof initialData.createdAt === 'string' ? new Date(initialData.createdAt) : initialData.createdAt) : 
          new Date(),
        updatedAt: new Date(),
        order: initialData?.order || 0,
        tags: initialData?.tags || [],
        images: allImages.map(img => {
          // Ensure all required properties have default values
          const imageUrl = img.url || '';
          const storagePath = img.storagePath || '';
          const thumbnailUrl = img.thumbnailUrl || img.url || '';
          const thumbnailStoragePath = img.thumbnailStoragePath || img.storagePath || '';
          const name = img.name || img.storagePath?.split('/').pop() || 'image';
          const size = img.size || 0;
          const type = img.type || 'image/jpeg';
          
          return {
            url: imageUrl,
            storagePath,
            thumbnailUrl,
            thumbnailStoragePath,
            name,
            size,
            type
          };
        })
      };

      await onSubmit(galleryItem);
      onSuccess?.();
    } catch (error) {
      console.error('Error saving gallery item:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save gallery item',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      setCurrentUploadIndex(0);
      setTotalUploads(0);
    }
  };

  const onSubmitForm = form.handleSubmit(handleFormSubmit);
  
  return (
    <Form {...form as any}>
      <form onSubmit={onSubmitForm} className="space-y-8">
        <div className="grid grid-cols-1 gap-8">
          {/* Image Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Gallery Images</CardTitle>
              <p className="text-sm text-muted-foreground">
                Upload one or more images for your gallery
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <FormField
                  control={form.control as any} // Type assertion for form control
                  name="images"
                  render={() => (
                    <FormItem>
                      <FormLabel>Images</FormLabel>
                      <FormControl>
                        <div 
                          className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer hover:bg-muted/50 transition-colors"
                          onDragOver={handleDragOver}
                          onDrop={handleDrop}
                          onClick={() => fileInputRef.current?.click()}
                        >
                          <div className="space-y-2">
                            <Upload className="w-8 h-8 mx-auto text-muted-foreground" />
                            <p className="text-sm text-muted-foreground">
                              <span className="font-medium text-primary">Click to upload</span> or drag and drop
                            </p>
                            <p className="text-xs text-muted-foreground">
                              SVG, PNG, JPG or GIF (max. 10MB per image)
                            </p>
                          </div>
                        </div>
                      </FormControl>
                      
                      {/* Upload Progress */}
                      {isUploading && (
                        <div className="mt-4 space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Uploading {currentUploadIndex} of {totalUploads} images</span>
                            <span>{Math.round(uploadProgress)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div 
                              className="bg-blue-600 h-2.5 rounded-full" 
                              style={{ width: `${uploadProgress}%` }}
                            />
                          </div>
                        </div>
                      )}
                      
                      {/* Image Previews */}
                      {(previewUrls.length > 0 || uploadedImages.length > 0) && (
                        <div className="mt-4">
                          <h4 className="text-sm font-medium mb-2">Selected Images</h4>
                          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            {/* Newly added images */}
                            {previewUrls.map((item, index) => (
                              <div key={index} className="relative group">
                                <div className="aspect-square rounded-md overflow-hidden border">
                                  <img
                                    src={item.url}
                                    alt={`Preview ${index + 1}`}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="icon"
                                  className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 rounded-full"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeImage(index);
                                  }}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                                <div className="mt-1 text-xs truncate">
                                  {item.file.name}
                                </div>
                              </div>
                            ))}

                            {/* Already uploaded images */}
                            {uploadedImages.map((image, index) => (
                              <div key={`uploaded-${index}`} className="relative group">
                                <div className="aspect-square rounded-md overflow-hidden border">
                                  <img
                                    src={image.url}
                                    alt={image.name}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="icon"
                                  className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 rounded-full"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeUploadedImage(index);
                                  }}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                                <div className="mt-1 text-xs truncate">
                                  {image.name}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <input
                        type="file"
                        ref={fileInputRef}
                        className="hidden"
                        accept="image/*"
                        multiple
                        onChange={handleImageChange}
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Gallery Item Details */}
          <Card>
            <CardHeader>
              <CardTitle>Gallery Item Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control as any} // Type assertion for form control
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter a description for your gallery item"
                        className="min-h-[100px]"
                        {...field}
                        disabled={loading || isUploading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={loading || isUploading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="published">Published</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="featured"
                  render={({ field }) => (
                    <FormItem>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="featured"
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={loading || isUploading}
                            className={`${field.value ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'}`}
                          />
                          <Label 
                            htmlFor="featured" 
                            className={field.value ? 'text-green-600 dark:text-green-400 font-medium' : ''}
                          >
                            Featured
                          </Label>
                        </div>
                        <p className={`text-xs ${field.value ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'}`}>
                          {field.value 
                            ? 'This item will be featured on the homepage.' 
                            : 'Featured items are displayed more prominently on the website.'}
                        </p>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-end space-x-4 border-t px-6 py-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={loading || isUploading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading || isUploading}
                className="bg-primary text-primary-foreground hover:bg-primary/90"
              >
                {loading || isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isUploading ? 'Uploading...' : 'Saving...'}
                  </>
                ) : (
                  <>{initialData ? 'Update' : 'Create'} Gallery Item</>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </form>
    </Form>
  );
}
