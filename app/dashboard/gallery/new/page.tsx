'use client';

import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { GalleryForm } from '../_components/gallery-form';
import { db } from '@/lib/firebase';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { GalleryItem } from '@/types/gallery';

export default function NewGalleryPage() {
  const { toast } = useToast();
  const router = useRouter();

  const handleSubmit = async (data: Omit<GalleryItem, 'id'>) => {
    try {
      console.log('Creating gallery item with data:', data);

      // Create the gallery item in Firestore
      const galleryData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      const docRef = await addDoc(collection(db, 'gallery'), galleryData);
      console.log('Gallery item created with ID:', docRef.id);

      toast({
        title: 'Success',
        description: 'Gallery item created successfully',
      });
      router.push('/dashboard/gallery');
    } catch (error) {
      console.error('Error creating gallery item:', error);
      toast({
        title: 'Error',
        description: 'Failed to create gallery item',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Add New Gallery Item</h1>
        <p className="text-sm text-muted-foreground">
          Add a new image to the gallery
        </p>
      </div>
      <div className="max-w-3xl">
        <GalleryForm
          initialData={null}
          onSubmit={handleSubmit}
          loading={false}
        />
      </div>
    </div>
  );
}
