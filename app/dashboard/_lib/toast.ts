'use client';

type ToastType = {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive';
};

export const toast = ({ title, description, variant = 'default' }: ToastType) => {
  // Using browser's alert as a fallback for now
  // In a real app, you'd want to use a proper toast library or component
  const message = [title, description].filter(Boolean).join(' - ');
  console.log(`[Toast ${variant}]:`, message);
  
  // This is a temporary implementation
  // In a real app, you'd dispatch an action to show a toast component
  if (typeof window !== 'undefined') {
    alert(message);
  }
};
