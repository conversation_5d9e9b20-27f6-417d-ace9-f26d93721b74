'use client';

import { StoreSettingsForm } from '../../_components/store-settings-form';

export default function StoreSettingsPage() {
  return (
    <div className="space-y-4">
      <StoreSettingsForm 
        initialData={{
          storeName: 'Adukrom Kingdom Store',
          storeCurrency: 'GHS',
          storeEmail: '',
          storePhone: '',
          storeAddress: '',
          storeCity: '',
          storeCountry: 'Ghana',
          storePostalCode: '',
          enableTaxes: true,
          taxRate: 15,
          enableShipping: true,
          enableCoupons: true,
          enableGuestCheckout: true,
          enableReviews: true,
          enableInventory: true,
          lowStockThreshold: 5,
          outOfStockThreshold: 0,
          allowBackorders: false,
          enableGiftCards: true,
          enableDigitalProducts: false,
          enableGiftWrapping: true,
          termsAndConditions: '',
          privacyPolicy: '',
          refundPolicy: ''
        }} 
      />
    </div>
  );
}
