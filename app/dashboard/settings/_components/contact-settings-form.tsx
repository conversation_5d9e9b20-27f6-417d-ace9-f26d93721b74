'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Loader2, MapPin, Mail, Phone, Clock, Save } from 'lucide-react';

const contactSchema = z.object({
  companyName: z.string().min(1, 'Company name is required'),
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State/Province is required'),
  postalCode: z.string().min(1, 'Postal code is required'),
  country: z.string().min(1, 'Country is required'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(1, 'Phone number is required'),
  hours: z.string().min(1, 'Business hours are required'),
  mapEmbedCode: z.string().optional(),
  additionalInfo: z.string().optional(),
});

type ContactSettingsFormValues = z.infer<typeof contactSchema>;

export function ContactSettingsForm({ initialData }: { initialData: Partial<ContactSettingsFormValues> }) {
  const { toast } = useToast();
  
  const form = useForm<ContactSettingsFormValues>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      companyName: initialData.companyName || '',
      address: initialData.address || '',
      city: initialData.city || '',
      state: initialData.state || '',
      postalCode: initialData.postalCode || '',
      country: initialData.country || '',
      email: initialData.email || '',
      phone: initialData.phone || '',
      hours: initialData.hours || 'Monday - Friday: 9:00 AM - 5:00 PM',
      mapEmbedCode: initialData.mapEmbedCode || '',
      additionalInfo: initialData.additionalInfo || '',
    },
  });

  const isLoading = form.formState.isSubmitting;

  async function onSubmit(data: ContactSettingsFormValues) {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: 'Success',
        description: 'Contact information has been updated.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error updating contact information:', error);
      toast({
        title: 'Error',
        description: 'Failed to update contact information. Please try again.',
        variant: 'destructive',
      });
    }
  }

  return (
    <Card className="bg-gradient-to-br from-royalBlue-900/80 via-royalBlue-800/80 to-forestGreen-900/60 backdrop-blur-xl border-none shadow-xl">
      <CardHeader>
        <CardTitle className="text-white">Contact Information</CardTitle>
        <CardDescription className="text-gray-300">
          Update your contact details and business information.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-white">Business Information</h3>
                
                <FormField
                  control={form.control}
                  name="companyName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Company Name</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Your Company Name" 
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">City</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="City" 
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">State/Province</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="State/Province" 
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="postalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Postal Code</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Postal Code" 
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Country</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Country" 
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Full Address</FormLabel>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input 
                          placeholder="123 Main St, City, Country" 
                          className="pl-10 bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-6">
                <h3 className="text-lg font-medium text-white">Contact Details</h3>
                
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Email Address</FormLabel>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input 
                          type="email"
                          placeholder="<EMAIL>"
                          className="pl-10 bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Phone Number</FormLabel>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input 
                          placeholder="+****************"
                          className="pl-10 bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="hours"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Business Hours</FormLabel>
                      <div className="relative">
                        <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input 
                          placeholder="Monday - Friday: 9:00 AM - 5:00 PM"
                          className="pl-10 bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </div>
                      <FormDescription className="text-gray-400">
                        Format: Day Range: Time - Time (e.g., Monday - Friday: 9:00 AM - 5:00 PM)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="mapEmbedCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Google Maps Embed Code</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="<iframe src='https://www.google.com/maps/embed?pb=...'"
                          className="min-h-[100px] bg-white/10 border-gray-600 text-white placeholder-gray-400 font-mono text-sm"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-gray-400">
                        Paste the iframe embed code from Google Maps
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="additionalInfo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Additional Information</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional contact information or special instructions..."
                      className="min-h-[100px] bg-white/10 border-gray-600 text-white placeholder-gray-400"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription className="text-gray-400">
                    This will be displayed on the contact page below your contact information
                  </FormDescription>
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4 pt-4 border-t border-gray-700">
              <Button 
                type="button" 
                variant="outline" 
                className="border-gray-600 text-white hover:bg-white/10 hover:text-white"
                onClick={() => form.reset()}
                disabled={isLoading}
              >
                Reset
              </Button>
              <Button 
                type="submit" 
                className="bg-royalBlue-600 hover:bg-royalBlue-700 text-white"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
