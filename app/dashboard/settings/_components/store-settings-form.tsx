'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Save, ShoppingCart, Package, Tag, Truck, Shield, CreditCard, Percent } from 'lucide-react';

const storeSchema = z.object({
  storeName: z.string().min(1, 'Store name is required'),
  storeCurrency: z.string().min(1, 'Currency is required'),
  storeEmail: z.string().email('Please enter a valid email address'),
  storePhone: z.string().optional(),
  storeAddress: z.string().optional(),
  storeCity: z.string().optional(),
  storeCountry: z.string().optional(),
  storePostalCode: z.string().optional(),
  enableTaxes: z.boolean().default(true),
  taxRate: z.number().min(0).max(100).default(0),
  enableShipping: z.boolean().default(true),
  enableCoupons: z.boolean().default(true),
  enableGuestCheckout: z.boolean().default(true),
  enableReviews: z.boolean().default(true),
  enableInventory: z.boolean().default(true),
  lowStockThreshold: z.number().min(0).default(5),
  outOfStockThreshold: z.number().min(0).default(0),
  allowBackorders: z.boolean().default(false),
  enableCashOnDelivery: z.boolean().default(true),
  enableCreditCard: z.boolean().default(true),
  enableBankTransfer: z.boolean().default(true),
  enablePaymentOnPickup: z.boolean().default(false),
  enableGiftWrapping: z.boolean().default(true),
  termsAndConditions: z.string().optional(),
  privacyPolicy: z.string().optional(),
  refundPolicy: z.string().optional(),
  enableDigitalProducts: z.boolean().default(false),
  enableGiftCards: z.boolean().default(false),
});

type StoreSettingsFormValues = z.infer<typeof storeSchema>;

export function StoreSettingsForm({ initialData }: { initialData: Partial<StoreSettingsFormValues> }) {
  const { toast } = useToast();
  
  const form = useForm<z.infer<typeof storeSchema>>({
    resolver: zodResolver(storeSchema) as any,
    defaultValues: {
      storeName: initialData.storeName || 'My Store',
      storeCurrency: initialData.storeCurrency || 'USD',
      storeEmail: initialData.storeEmail || '',
      storePhone: initialData.storePhone || '',
      storeAddress: initialData.storeAddress || '',
      storeCity: initialData.storeCity || '',
      storeCountry: initialData.storeCountry || 'Ghana',
      storePostalCode: initialData.storePostalCode || '',
      enableTaxes: initialData.enableTaxes ?? true,
      taxRate: initialData.taxRate ?? 15,
      enableShipping: initialData.enableShipping ?? true,
      enableCoupons: initialData.enableCoupons ?? true,
      enableGuestCheckout: initialData.enableGuestCheckout ?? true,
      enableReviews: initialData.enableReviews ?? true,
      enableInventory: initialData.enableInventory ?? true,
      lowStockThreshold: initialData.lowStockThreshold ?? 5,
      outOfStockThreshold: initialData.outOfStockThreshold ?? 0,
      allowBackorders: initialData.allowBackorders ?? false,
      enableCashOnDelivery: initialData.enableCashOnDelivery ?? true,
      enableCreditCard: initialData.enableCreditCard ?? true,
      enableBankTransfer: initialData.enableBankTransfer ?? true,
      enablePaymentOnPickup: initialData.enablePaymentOnPickup ?? false,
      enableGiftWrapping: initialData.enableGiftWrapping ?? true,
      enableDigitalProducts: initialData.enableDigitalProducts ?? false,
      enableGiftCards: initialData.enableGiftCards ?? false,
      termsAndConditions: initialData.termsAndConditions || '',
      privacyPolicy: initialData.privacyPolicy || '',
      refundPolicy: initialData.refundPolicy || '',
    },
  });

  const isLoading = form.formState.isSubmitting;

  async function onSubmit(data: StoreSettingsFormValues) {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: 'Success',
        description: 'Store settings have been updated.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error updating store settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update store settings. Please try again.',
        variant: 'destructive',
      });
    }
  }

  return (
    <div className="space-y-6">
      <Card className="border-dashed border-2 border-amber-500/30 bg-amber-500/5">
        <CardHeader className="pb-3">
          <div className="flex items-center">
            <Shield className="h-5 w-5 mr-2 text-amber-400" />
            <span className="text-amber-400 text-sm font-medium">Super Admin Only</span>
          </div>
          <CardTitle className="text-white">Store Settings</CardTitle>
          <CardDescription className="text-gray-300">
            Configure your online store settings, payment methods, and shipping options.
          </CardDescription>
        </CardHeader>
      </Card>

      <Card className="bg-gradient-to-br from-royalBlue-900/80 via-royalBlue-800/80 to-forestGreen-900/60 backdrop-blur-xl border-none shadow-xl">
        <CardContent className="pt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <div>
                <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                  <ShoppingCart className="h-5 w-5 mr-2 text-royalBlue-300" />
                  General Store Settings
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="storeName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Store Name</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="My Awesome Store" 
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="storeCurrency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Store Currency</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="bg-white/10 border-gray-600 text-white">
                              <SelectValue placeholder="Select currency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-gray-800 border-gray-700">
                            {[
                              { value: 'USD', label: 'US Dollar ($)' },
                              { value: 'EUR', label: 'Euro (€)' },
                              { value: 'GBP', label: 'British Pound (£)' },
                              { value: 'GHS', label: 'Ghanaian Cedi (GH₵)' },
                              { value: 'NGN', label: 'Nigerian Naira (₦)' },
                              { value: 'ZAR', label: 'South African Rand (R)' },
                              { value: 'KES', label: 'Kenyan Shilling (KSh)' },
                              { value: 'XAF', label: 'CFA Franc (FCFA)' },
                            ].map((currency) => (
                              <SelectItem 
                                key={currency.value} 
                                value={currency.value}
                                className="hover:bg-gray-700"
                              >
                                {currency.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  <FormField
                    control={form.control}
                    name="storeEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Store Email</FormLabel>
                        <FormControl>
                          <Input 
                            type="email"
                            placeholder="<EMAIL>" 
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="storePhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Store Phone</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="+****************" 
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                  <Percent className="h-5 w-5 mr-2 text-royalBlue-300" />
                  Tax Settings
                </h3>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="enableTaxes"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Enable Taxes</FormLabel>
                          <FormDescription className="text-gray-400">
                            Enable tax calculation for orders
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch('enableTaxes') && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pl-6">
                      <FormField
                        control={form.control}
                        name="taxRate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Tax Rate (%)</FormLabel>
                            <FormControl>
                              <Input 
                                type="number"
                                min="0"
                                max="100"
                                step="0.01"
                                className="bg-white/10 border-gray-600 text-white"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                  <Truck className="h-5 w-5 mr-2 text-royalBlue-300" />
                  Shipping & Inventory
                </h3>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="enableShipping"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Enable Shipping</FormLabel>
                          <FormDescription className="text-gray-400">
                            Allow customers to ship products to their address
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="enableInventory"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Inventory Management</FormLabel>
                          <FormDescription className="text-gray-400">
                            Track product inventory levels
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch('enableInventory') && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pl-6">
                      <FormField
                        control={form.control}
                        name="lowStockThreshold"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Low Stock Threshold</FormLabel>
                            <FormControl>
                              <Input 
                                type="number"
                                min="0"
                                className="bg-white/10 border-gray-600 text-white"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormDescription className="text-gray-400">
                              Get notified when stock is low
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="outOfStockThreshold"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Out of Stock Threshold</FormLabel>
                            <FormControl>
                              <Input 
                                type="number"
                                min="0"
                                className="bg-white/10 border-gray-600 text-white"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormDescription className="text-gray-400">
                              When to show "Out of Stock"
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                  <CreditCard className="h-5 w-5 mr-2 text-royalBlue-300" />
                  Payment Methods
                </h3>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="enableCreditCard"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Credit/Debit Cards</FormLabel>
                          <FormDescription className="text-gray-400">
                            Accept payments via credit and debit cards
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="enableBankTransfer"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Bank Transfer</FormLabel>
                          <FormDescription className="text-gray-400">
                            Allow customers to pay via bank transfer
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="enableCashOnDelivery"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Cash on Delivery</FormLabel>
                          <FormDescription className="text-gray-400">
                            Allow customers to pay when they receive their order
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="enablePaymentOnPickup"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Pay on Pickup</FormLabel>
                          <FormDescription className="text-gray-400">
                            Allow customers to pay when they pick up their order
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                  <Package className="h-5 w-5 mr-2 text-royalBlue-300" />
                  Product Options
                </h3>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="enableDigitalProducts"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Digital Products</FormLabel>
                          <FormDescription className="text-gray-400">
                            Allow selling digital products and downloads
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="enableGiftCards"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Gift Cards</FormLabel>
                          <FormDescription className="text-gray-400">
                            Enable gift card functionality
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                  <Tag className="h-5 w-5 mr-2 text-royalBlue-300" />
                  Shopping Experience
                </h3>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="enableCoupons"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Coupons & Discounts</FormLabel>
                          <FormDescription className="text-gray-400">
                            Enable coupon and discount code functionality
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="enableGuestCheckout"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Guest Checkout</FormLabel>
                          <FormDescription className="text-gray-400">
                            Allow customers to checkout without creating an account
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="enableReviews"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-white">Product Reviews</FormLabel>
                          <FormDescription className="text-gray-400">
                            Allow customers to submit product reviews
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-royalBlue-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-4 border-t border-gray-700">
                <Button 
                  type="button" 
                  variant="outline" 
                  className="border-gray-600 text-white hover:bg-white/10 hover:text-white"
                  onClick={() => form.reset()}
                  disabled={isLoading}
                >
                  Reset
                </Button>
                <Button 
                  type="submit" 
                  className="bg-royalBlue-600 hover:bg-royalBlue-700 text-white"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Store Settings
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      <div className="bg-gradient-to-r from-amber-900/30 to-transparent p-4 rounded-lg border border-amber-800/50">
        <div className="flex">
          <div className="flex-shrink-0">
            <Shield className="h-5 w-5 text-amber-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-200">Important Notice</h3>
            <div className="mt-2 text-sm text-amber-100">
              <p>
                Changes to store settings may affect your customers' shopping experience. 
                Please review all changes carefully before saving.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
