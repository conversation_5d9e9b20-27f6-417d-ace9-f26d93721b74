'use client';

"use client";

import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Save, CreditCard, Banknote, Building, Shield, Wallet } from "lucide-react";

// Define account types as const for type safety
const ACCOUNT_TYPES = {
  CHECKING: 'checking',
  SAVINGS: 'savings',
  BUSINESS: 'business',
  MERCHANT: 'merchant'
} as const;

type AccountType = typeof ACCOUNT_TYPES[keyof typeof ACCOUNT_TYPES];

// Create a type for the account type enum
const AccountTypeEnum = z.nativeEnum(ACCOUNT_TYPES);

// Define the form schema with Zod
const bankingSchema = z.object({
  accountType: AccountTypeEnum,
  bankName: z.string().min(1, 'Bank name is required'),
  accountName: z.string().min(1, 'Account name is required'),
  accountNumber: z.string().min(1, 'Account number is required'),
  routingNumber: z.string().min(1, 'Routing number is required'),
  swiftCode: z.string().optional(),
  iban: z.string().optional(),
  currency: z.string().default('USD'),
  taxId: z.string().optional(),
  isPrimary: z.boolean().default(false),
  notes: z.string().optional(),
});

// Infer the form values type from the schema
type FormValues = z.infer<typeof bankingSchema>;

interface BankingSettingsFormProps {
  initialData?: Partial<FormValues>;
  onSuccess?: () => void;
}

export function BankingSettingsForm({ 
  initialData = {}, 
  onSuccess 
}: BankingSettingsFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Initialize form with default values
  const defaultValues: FormValues = {
    accountType: initialData?.accountType ?? ACCOUNT_TYPES.CHECKING,
    bankName: initialData?.bankName ?? '',
    accountName: initialData?.accountName ?? '',
    accountNumber: initialData?.accountNumber ?? '',
    routingNumber: initialData?.routingNumber ?? '',
    swiftCode: initialData?.swiftCode ?? '',
    iban: initialData?.iban ?? '',
    currency: initialData?.currency ?? 'USD',
    taxId: initialData?.taxId ?? '',
    isPrimary: initialData?.isPrimary ?? false,
    notes: initialData?.notes ?? '',
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(bankingSchema) as any,
    defaultValues,
    mode: "onChange",
  });

  const { control, handleSubmit, formState } = form;
  const isLoading = formState.isSubmitting || isSubmitting;

  // Handle form submission
  const onSubmit: SubmitHandler<FormValues> = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success",
        description: "Banking settings saved successfully",
        variant: "default",
      });
      
      if (onSuccess) {
        onSuccess();
      } else {
        // Refresh the page to show updated data
        router.refresh();
      }
    } catch (error) {
      console.error("Error saving banking settings:", error);
      toast({
        title: "Error",
        description: "Failed to save banking settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card className="border-dashed border-2 border-amber-500/30 bg-amber-500/5">
        <CardHeader className="pb-3">
          <div className="flex items-center">
            <Shield className="h-5 w-5 mr-2 text-amber-400" />
            <span className="text-amber-400 text-sm font-medium">Admin Only</span>
          </div>
          <CardTitle className="text-white">Banking Information</CardTitle>
          <CardDescription className="text-gray-300">
            Manage your banking and payment processing settings. This information is securely encrypted.
          </CardDescription>
        </CardHeader>
      </Card>

      <Card className="bg-gradient-to-br from-royalBlue-900/80 via-royalBlue-800/80 to-forestGreen-900/60 backdrop-blur-xl border-none shadow-xl">
        <CardContent className="pt-6">
          <Form {...form}>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Account Type */}
                <FormField
                  control={control}
                  name="accountType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Account Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-white/10 border-gray-600 text-white">
                            <SelectValue placeholder="Select account type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-gray-800 border-gray-700">
                          {Object.entries(ACCOUNT_TYPES).map(([key, value]) => (
                            <SelectItem 
                              key={key}
                              value={value}
                              className="hover:bg-gray-700"
                            >
                              <div className="flex items-center">
                                {value === ACCOUNT_TYPES.CHECKING ? (
                                  <CreditCard className="h-4 w-4 mr-2" />
                                ) : value === ACCOUNT_TYPES.SAVINGS ? (
                                  <Banknote className="h-4 w-4 mr-2" />
                                ) : value === ACCOUNT_TYPES.BUSINESS ? (
                                  <Building className="h-4 w-4 mr-2" />
                                ) : (
                                  <Wallet className="h-4 w-4 mr-2" />
                                )}
                                {value.charAt(0).toUpperCase() + value.slice(1)} Account
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Bank Name */}
                <FormField
                  control={form.control}
                  name="bankName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Bank Name</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Bank of America" 
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Account Name */}
                <FormField
                  control={form.control}
                  name="accountName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Account Name</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="John Doe" 
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="accountNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Account Number</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="•••• •••• •••• ••••" 
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400 font-mono tracking-widest"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="routingNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Routing Number</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="*********" 
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400 font-mono"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="swiftCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">SWIFT/BIC Code</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="BOFAUS3N" 
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400 font-mono"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Currency</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="bg-white/10 border-gray-600 text-white">
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-gray-800 border-gray-700 max-h-60">
                          {[
                            { value: 'USD', label: 'US Dollar (USD)' },
                            { value: 'EUR', label: 'Euro (EUR)' },
                            { value: 'GBP', label: 'British Pound (GBP)' },
                            { value: 'GHS', label: 'Ghanaian Cedi (GHS)' },
                            { value: 'NGN', label: 'Nigerian Naira (NGN)' },
                            { value: 'ZAR', label: 'South African Rand (ZAR)' },
                            { value: 'KES', label: 'Kenyan Shilling (KES)' },
                            { value: 'XAF', label: 'CFA Franc (XAF)' },
                          ].map((currency) => (
                            <SelectItem 
                              key={currency.value} 
                              value={currency.value}
                              className="hover:bg-gray-700"
                            >
                              {currency.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="iban"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">IBAN (International)</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="GB29 NWBK 6016 1331 9268 19" 
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400 font-mono"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription className="text-gray-400">
                        Required for international transfers
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="taxId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Tax ID / EIN</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="12-3456789" 
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400 font-mono"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription className="text-gray-400">
                        For tax reporting purposes
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Notes</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Any additional notes about this account" 
                        className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription className="text-gray-400">
                      Internal notes about this bank account (not visible to customers)
                    </FormDescription>
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-4 pt-4 border-t border-gray-700">
                <Button 
                  type="button" 
                  variant="outline" 
                  className="border-gray-600 text-white hover:bg-white/10 hover:text-white"
                  onClick={() => form.reset()}
                  disabled={isLoading}
                >
                  Reset
                </Button>
                <Button 
                  type="submit" 
                  className="bg-royalBlue-600 hover:bg-royalBlue-700 text-white"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Banking Information
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      <div className="bg-gradient-to-r from-amber-900/30 to-transparent p-4 rounded-lg border border-amber-800/50">
        <div className="flex">
          <div className="flex-shrink-0">
            <Shield className="h-5 w-5 text-amber-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-200">Security Notice</h3>
            <div className="mt-2 text-sm text-amber-100">
              <p>
                All banking information is encrypted and stored securely. We use bank-level encryption to protect your data.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
