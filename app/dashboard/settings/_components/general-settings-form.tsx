'use client';

import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Loader2, Save } from 'lucide-react';

const generalSettingsSchema = z.object({
  siteName: z.string().min(1, 'Site name is required'),
  siteDescription: z.string().min(1, 'Site description is required'),
  siteKeywords: z.string(),
  maintenanceMode: z.boolean().default(false),
});

type GeneralSettingsFormValues = z.infer<typeof generalSettingsSchema>;

interface GeneralSettingsFormProps {
  initialData: Partial<GeneralSettingsFormValues>;
  onSuccess?: () => void;
}

export function GeneralSettingsForm({ initialData, onSuccess }: GeneralSettingsFormProps) {
  const { toast } = useToast();
  
  const form = useForm<GeneralSettingsFormValues>({
    resolver: zodResolver(generalSettingsSchema) as any, // Type assertion to fix resolver type
    defaultValues: {
      siteName: initialData.siteName ?? '',
      siteDescription: initialData.siteDescription ?? '',
      siteKeywords: initialData.siteKeywords ?? '',
      maintenanceMode: initialData.maintenanceMode ?? false,
    },
  });

  const { control, handleSubmit, formState: { isSubmitting } } = form;
  const isLoading = isSubmitting;

  const onSubmit: SubmitHandler<GeneralSettingsFormValues> = async (data) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: 'Success',
        description: 'General settings have been updated.',
        variant: 'default',
      });
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update settings. Please try again.',
        variant: 'destructive',
      });
    }
  }

  return (
    <Card className="bg-gradient-to-br from-royalBlue-900/80 via-royalBlue-800/80 to-forestGreen-900/60 backdrop-blur-xl border-none shadow-xl">
      <CardHeader>
        <CardTitle className="text-white">General Settings</CardTitle>
        <CardDescription className="text-gray-300">
          Configure the basic settings for your website.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={control}
              name="siteName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Site Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Site Name" 
                      className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                      {...field} 
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="siteDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Site Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="A brief description of your website"
                      className="min-h-[100px] bg-white/10 border-gray-600 text-white placeholder-gray-400"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="siteKeywords"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Site Keywords (comma separated)</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="keyword1, keyword2, keyword3"
                      className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription className="text-gray-400">
                    Keywords help with SEO. Separate them with commas.
                  </FormDescription>
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="maintenanceMode"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-700 p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-white">Maintenance Mode</FormLabel>
                    <FormDescription className="text-gray-400">
                      When enabled, the website will be in maintenance mode and only accessible to admins.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-royalBlue-500"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4">
              <Button 
                type="button" 
                variant="outline" 
                className="border-gray-600 text-white hover:bg-white/10 hover:text-white"
                onClick={() => form.reset()}
                disabled={isLoading}
              >
                Reset
              </Button>
              <Button 
                type="submit" 
                className="bg-royalBlue-600 hover:bg-royalBlue-700 text-white"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
