'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Loader2, Save } from 'lucide-react';

const seoSchema = z.object({
  metaTitle: z.string().min(1, 'Meta title is required').max(60, 'Meta title should not exceed 60 characters'),
  metaDescription: z.string().min(1, 'Meta description is required').max(160, 'Meta description should not exceed 160 characters'),
  metaKeywords: z.string(),
  ogTitle: z.string().optional(),
  ogDescription: z.string().optional(),
  ogImage: z.string().url('Please enter a valid URL').or(z.literal('')).optional(),
  twitterCard: z.string().optional(),
  canonicalUrl: z.string().url('Please enter a valid URL').or(z.literal('')).optional(),
});

type SeoSettingsFormValues = z.infer<typeof seoSchema>;

export function SeoSettingsForm({ initialData }: { initialData: Partial<SeoSettingsFormValues> }) {
  const { toast } = useToast();
  
  const form = useForm<SeoSettingsFormValues>({
    resolver: zodResolver(seoSchema),
    defaultValues: {
      metaTitle: initialData.metaTitle || '',
      metaDescription: initialData.metaDescription || '',
      metaKeywords: initialData.metaKeywords || '',
      ogTitle: initialData.ogTitle || '',
      ogDescription: initialData.ogDescription || '',
      ogImage: initialData.ogImage || '',
      twitterCard: initialData.twitterCard || 'summary_large_image',
      canonicalUrl: initialData.canonicalUrl || '',
    },
  });

  const isLoading = form.formState.isSubmitting;

  async function onSubmit(data: SeoSettingsFormValues) {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: 'Success',
        description: 'SEO settings have been updated.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error updating SEO settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update SEO settings. Please try again.',
        variant: 'destructive',
      });
    }
  }

  return (
    <Card className="bg-gradient-to-br from-royalBlue-900/80 via-royalBlue-800/80 to-forestGreen-900/60 backdrop-blur-xl border-none shadow-xl">
      <CardHeader>
        <CardTitle className="text-white">SEO & Metadata</CardTitle>
        <CardDescription className="text-gray-300">
          Configure how your website appears in search engines and social media.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-white">Search Engine Optimization</h3>
                
                <FormField
                  control={form.control}
                  name="metaTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Meta Title</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Page Title - Site Name" 
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription className="text-gray-400">
                        Recommended: 50-60 characters
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="metaDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Meta Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="A brief description of this page"
                          className="min-h-[100px] bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-gray-400">
                        Recommended: 150-160 characters
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="metaKeywords"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Meta Keywords</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="keyword1, keyword2, keyword3"
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription className="text-gray-400">
                        Separate keywords with commas
                      </FormDescription>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="canonicalUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Canonical URL</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="https://example.com/page"
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription className="text-gray-400">
                        The canonical URL that this page should point to
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-6">
                <h3 className="text-lg font-medium text-white">Social Media</h3>
                
                <FormField
                  control={form.control}
                  name="ogTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">OpenGraph Title</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Share Title" 
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription className="text-gray-400">
                        Title for social media shares (defaults to meta title if empty)
                      </FormDescription>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="ogDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">OpenGraph Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Share description"
                          className="min-h-[80px] bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-gray-400">
                        Description for social media shares (defaults to meta description if empty)
                      </FormDescription>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="ogImage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">OpenGraph Image URL</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="https://example.com/image.jpg"
                          className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription className="text-gray-400">
                        Recommended size: 1200x630 pixels
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="twitterCard"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Twitter Card Type</FormLabel>
                      <select
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white/10 border-gray-600 text-white"
                        {...field}
                      >
                        <option value="summary">Summary</option>
                        <option value="summary_large_image">Summary with large image</option>
                        <option value="app">App</option>
                        <option value="player">Player</option>
                      </select>
                      <FormDescription className="text-gray-400">
                        The type of Twitter card to use
                      </FormDescription>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4 border-t border-gray-700">
              <Button 
                type="button" 
                variant="outline" 
                className="border-gray-600 text-white hover:bg-white/10 hover:text-white"
                onClick={() => form.reset()}
                disabled={isLoading}
              >
                Reset
              </Button>
              <Button 
                type="submit" 
                className="bg-royalBlue-600 hover:bg-royalBlue-700 text-white"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
