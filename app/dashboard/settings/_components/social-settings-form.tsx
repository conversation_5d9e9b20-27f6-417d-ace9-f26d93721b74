'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Loader2, Save, Globe, Github } from 'lucide-react';

const socialSchema = z.object({
  facebook: z.string().url('Please enter a valid URL').or(z.literal('')),
  twitter: z.string().url('Please enter a valid URL').or(z.literal('')),
  instagram: z.string().url('Please enter a valid URL').or(z.literal('')),
  linkedin: z.string().url('Please enter a valid URL').or(z.literal('')),
  youtube: z.string().url('Please enter a valid URL').or(z.literal('')),
  tiktok: z.string().url('Please enter a valid URL').or(z.literal('')),
  discord: z.string().url('Please enter a valid URL').or(z.literal('')),
  github: z.string().url('Please enter a valid URL').or(z.literal('')),
  website: z.string().url('Please enter a valid URL').or(z.literal('')),
  shareTitle: z.string().optional(),
  shareDescription: z.string().optional(),
  shareImage: z.string().url('Please enter a valid URL').or(z.literal('')).optional(),
});

type SocialSettingsFormValues = z.infer<typeof socialSchema>;

export function SocialSettingsForm({ initialData }: { initialData: Partial<SocialSettingsFormValues> }) {
  const { toast } = useToast();
  
  const form = useForm<SocialSettingsFormValues>({
    resolver: zodResolver(socialSchema),
    defaultValues: {
      facebook: initialData.facebook || '',
      twitter: initialData.twitter || '',
      instagram: initialData.instagram || '',
      linkedin: initialData.linkedin || '',
      youtube: initialData.youtube || '',
      tiktok: initialData.tiktok || '',
      discord: initialData.discord || '',
      github: initialData.github || '',
      website: initialData.website || '',
      shareTitle: initialData.shareTitle || '',
      shareDescription: initialData.shareDescription || '',
      shareImage: initialData.shareImage || '',
    },
  });

  const isLoading = form.formState.isSubmitting;

  async function onSubmit(data: SocialSettingsFormValues) {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: 'Success',
        description: 'Social media settings have been updated.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error updating social media settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update social media settings. Please try again.',
        variant: 'destructive',
      });
    }
  }

  const socialPlatforms = [
    {
      name: 'Website',
      field: 'website',
      icon: Globe,
      placeholder: 'https://example.com',
      description: 'Your main website URL',
    },
    {
      name: 'Facebook',
      field: 'facebook',
      icon: () => <span className="text-blue-600">f</span>,
      placeholder: 'https://facebook.com/yourpage',
      description: 'Your Facebook page or profile URL',
    },
    {
      name: 'Twitter',
      field: 'twitter',
      icon: () => <span className="text-blue-400">𝕏</span>,
      placeholder: 'https://twitter.com/yourhandle',
      description: 'Your Twitter profile URL',
    },
    {
      name: 'Instagram',
      field: 'instagram',
      icon: () => <span className="text-pink-600">IG</span>,
      placeholder: 'https://instagram.com/yourprofile',
      description: 'Your Instagram profile URL',
    },
    {
      name: 'LinkedIn',
      field: 'linkedin',
      icon: () => <span className="text-blue-700">in</span>,
      placeholder: 'https://linkedin.com/company/yourcompany',
      description: 'Your LinkedIn company or profile URL',
    },
    {
      name: 'YouTube',
      field: 'youtube',
      icon: () => <span className="text-red-600">▶️</span>,
      placeholder: 'https://youtube.com/yourchannel',
      description: 'Your YouTube channel URL',
    },
    {
      name: 'TikTok',
      field: 'tiktok',
      icon: () => <span>🎵</span>,
      placeholder: 'https://tiktok.com/@yourusername',
      description: 'Your TikTok profile URL',
    },
    {
      name: 'Discord',
      field: 'discord',
      icon: () => <span className="text-indigo-500">💬</span>,
      placeholder: 'https://discord.gg/yourinvite',
      description: 'Your Discord server invite URL',
    },
    {
      name: 'GitHub',
      field: 'github',
      icon: Github,
      placeholder: 'https://github.com/yourusername',
      description: 'Your GitHub profile or organization URL',
    },
  ];

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-royalBlue-900/80 via-royalBlue-800/80 to-forestGreen-900/60 backdrop-blur-xl border-none shadow-xl">
        <CardHeader>
          <CardTitle className="text-white">Social Media Profiles</CardTitle>
          <CardDescription className="text-gray-300">
            Add links to your social media profiles to connect with your audience.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 gap-6">
                {socialPlatforms.map((platform) => (
                  <FormField
                    key={platform.field}
                    control={form.control}
                    name={platform.field as keyof SocialSettingsFormValues}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white flex items-center">
                          <platform.icon className="h-4 w-4 mr-2" />
                          {platform.name}
                        </FormLabel>
                        <FormControl>
                          <Input 
                            placeholder={platform.placeholder}
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription className="text-gray-400">
                          {platform.description}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}
              </div>

              <div className="border-t border-gray-700 pt-6 mt-6">
                <h3 className="text-lg font-medium text-white mb-4">Social Sharing Settings</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="shareTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Default Share Title</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Share this content"
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription className="text-gray-400">
                          Default title when sharing on social media
                        </FormDescription>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="shareDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Default Share Description</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Check out this amazing content!"
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription className="text-gray-400">
                          Default description when sharing on social media
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="mt-4">
                  <FormField
                    control={form.control}
                    name="shareImage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Default Share Image</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="https://example.com/images/share-image.jpg"
                            className="bg-white/10 border-gray-600 text-white placeholder-gray-400"
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription className="text-gray-400">
                          Default image URL to use when sharing on social media (1200x630px recommended)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-4 border-t border-gray-700">
                <Button 
                  type="button" 
                  variant="outline" 
                  className="border-gray-600 text-white hover:bg-white/10 hover:text-white"
                  onClick={() => form.reset()}
                  disabled={isLoading}
                >
                  Reset
                </Button>
                <Button 
                  type="submit" 
                  className="bg-royalBlue-600 hover:bg-royalBlue-700 text-white"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
