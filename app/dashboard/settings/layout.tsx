'use client';

import { ReactNode, useState } from 'react';
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { SettingsTabs } from "./_components/settings-tabs";
import { Shield } from "lucide-react";

export default function SettingsLayout({
  children,
}: {
  children: ReactNode;
}) {
  // In a real app, this would come from your authentication context
  const [isSuperAdmin] = useState(true);

  return (
    <div className="p-4 md:p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl md:text-3xl font-bold text-black">
          Settings
        </h1>
        {isSuperAdmin && (
          <div className="flex items-center space-x-2 bg-amber-100 text-amber-800 px-3 py-1.5 rounded-md text-sm border border-amber-200">
            <Shield className="h-4 w-4" />
            <span>Super Admin Mode</span>
          </div>
        )}
      </div>

      <Card className="bg-white border border-gray-200 shadow-sm">
        <Tabs defaultValue="general" className="w-full">
          <div className="border-b border-gray-200">
            <SettingsTabs isSuperAdmin={isSuperAdmin} />
          </div>
          <div className="p-6">
            {children}
          </div>
        </Tabs>
      </Card>
    </div>
  );
}
