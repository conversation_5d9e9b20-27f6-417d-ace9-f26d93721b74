import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON><PERSON> } from "next/font/google"
import "./globals.css"
import { AdminAuthProvider } from "@/contexts/AdminAuthContext"

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
  display: "swap",
})

export const metadata: Metadata = {
  title: "Adukrom Kingdom - Admin Dashboard",
  description: "Administrative dashboard for Adukrom Kingdom management",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={montserrat.variable}>
      <body>
        <AdminAuthProvider>
          {children}
        </AdminAuthProvider>
      </body>
    </html>
  )
}
