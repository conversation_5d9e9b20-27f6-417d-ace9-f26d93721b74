@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

:root {
  /* Brand Colors */
  --royalBlue: #1e3a8a;
  --royalBlue-light: #3b5b9d;
  --royalBlue-dark: #142a5e;
  
  --royalGold: #d4af37;
  --royalGold-light: #ddc16a;
  --royalGold-dark: #b38f2d;
  
  --forestGreen: #228b22;
  --forestGreen-light: #2e9e2e;
  --forestGreen-dark: #1a6b1a;
  
  /* Ivory Color Palette */
  --ivory-50: #fdfcf5;
  --ivory-100: #fdf6e3;
  --ivory-200: #f9f2d9;
  --ivory-300: #f5e9c7;
  --ivory-400: #f0e0b5;
  --ivory-500: #e8d49e;
  --ivory-600: #e0c887;
  --ivory-700: #d8bc70;
  --ivory-800: #d0b059;
  --ivory-900: #c8a442;
  
  /* Theme Colors */
  --background: #ffffff;
  --foreground: #1a1a1a;
  --primary: var(--royalBlue);
  --primary-foreground: #ffffff;
  --secondary: var(--royalGold);
  --secondary-foreground: #000000;
  --accent: var(--forestGreen);
  --accent-foreground: #ffffff;
  --muted: #f5f5f5;
  --muted-foreground: #666666;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: var(--royalBlue-light);
  --popover: #ffffff;
  --popover-foreground: #1a1a1a;
  --card: #ffffff;
  --card-foreground: #1a1a1a;
  --radius: 0.5rem;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* Base styles */
html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Montserrat', sans-serif;
  background-color: var(--ivory-100);
  color: var(--foreground);
  margin: 0;
  padding: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--royalGold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--royalGold-dark);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* Glass effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Royal pattern background */
.royal-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Import animations after variables are defined */
@import '../styles/animations.css';

/* Sidebar theme variables */
:root {
  --sidebar-background: 0 0% 100%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}

/* Dropdown menu styles */
[data-radix-popper-content-wrapper] {
  z-index: 1000;
}

/* Ensure dropdown content is visible */
[data-radix-popper-content-wrapper] > * {
  background-color: var(--popover) !important;
  color: var(--popover-foreground) !important;
  border: 1px solid var(--border) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Fix dropdown menu items */
[data-radix-menu-item] {
  color: var(--foreground) !important;
}

[data-radix-menu-item][data-highlighted] {
  background-color: var(--muted) !important;
  color: var(--foreground) !important;
}

/* Form elements */
input, select, textarea {
  background-color: var(--background) !important;
  color: var(--foreground) !important;
  border-color: var(--border) !important;
}

input:focus, select:focus, textarea:focus {
  border-color: var(--ring) !important;
  box-shadow: 0 0 0 2px var(--ring) !important;
}

/* Base styles for elements */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: var(--background);
  color: var(--foreground);
}

/* Layout fixes for sidebar and main content */
.sidebar-layout {
  display: flex;
  min-height: 100vh;
}

.sidebar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 50; /* Highest - sidebar should be on top */
}

.main-content-with-sidebar {
  flex: 1;
  min-height: 100vh;
  margin-left: 0;
  transition: margin-left 0.3s ease-in-out;
}

/* We're now handling the sidebar width and main content margin in the layout component */
@media (min-width: 768px) {
  /* Base styles for desktop are now handled with Tailwind classes in the layout component */
}

/* Z-index hierarchy:
 * 50 - Sidebar (highest)
 * 40 - Header and mobile overlay (middle)
 * 30 - Dropdowns and modals (lower)
 * 10 - Main content (lowest)
 */

.header-sticky {
  position: sticky;
  top: 0;
  z-index: 40; /* Below sidebar, above main content */
}

.sidebar-overlay {
  position: fixed;
  inset: 0;
  z-index: 40; /* Same as header, below sidebar */
  background-color: rgba(0, 0, 0, 0.5);
}

/* Ensure main content doesn't interfere */
.main-content {
  position: relative;
  z-index: 10;
}
