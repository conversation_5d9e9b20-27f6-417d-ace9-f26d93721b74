'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminAuth } from '@/contexts/AdminAuthContext'
import { Loader2 } from 'lucide-react'

export default function HomePage() {
  const { user, loading, isAdmin } = useAdminAuth()
  const router = useRouter()
  const [isRedirecting, setIsRedirecting] = useState(false)

  useEffect(() => {
    if (!loading) {
      setIsRedirecting(true)
      if (user && isAdmin) {
        router.push('/dashboard')
      } else {
        router.push('/login')
      }
    }
  }, [user, isAdmin, loading, router])

  if (loading || isRedirecting) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return null
}