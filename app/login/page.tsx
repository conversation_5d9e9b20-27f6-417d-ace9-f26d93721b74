'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminAuth } from '@/contexts/AdminAuthContext'
import AdminLogin from '@/components/AdminLogin'
import { Loader2 } from 'lucide-react'

export default function LoginPage() { 
  const { user, loading, isAdmin } = useAdminAuth()
  const router = useRouter()
  const [isRedirecting, setIsRedirecting] = useState(false)
  
  // Redirect to admin dashboard if already logged in
  useEffect(() => {
    if (!loading && user && isAdmin) {
      setIsRedirecting(true)
      router.push('/dashboard')
    }
  }, [user, loading, isAdmin, router])

  // Show loading state while checking auth status
  if (loading || isRedirecting) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }
  
  return <AdminLogin />
}
