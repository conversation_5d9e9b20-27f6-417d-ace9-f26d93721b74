import { NextResponse } from 'next/server';
import { adminAuth, adminFirestore } from '@/lib/firebaseAdmin';

// Helper to sync user data to Firestore
async function syncUserToFirestore(user: any) {
  const userRef = adminFirestore.collection('users').doc(user.uid);
  const userData = {
    uid: user.uid,
    email: user.email || '',
    displayName: user.displayName || '',
    photoURL: user.photoURL || '',
    emailVerified: user.emailVerified,
    disabled: user.disabled,
    metadata: {
      creationTime: user.metadata.creationTime,
      lastSignInTime: user.metadata.lastSignInTime,
    },
    updatedAt: new Date().toISOString(),
  };

  // Get existing role if it exists
  const doc = await userRef.get();
  const existingRole = doc.exists ? doc.data()?.role : 'user';
  
  // Merge with existing data, preserving role
  await userRef.set(
    { 
      ...userData,
      role: existingRole,
    },
    { merge: true }
  );

  return {
    ...userData,
    role: existingRole,
  };
}

export async function GET() {
  try {
    // List all users from Firebase Auth
    const auth = adminAuth;
    const authUsers = await auth.listUsers();
    
    // Sync each user to Firestore and get their data
    const users = await Promise.all(
      authUsers.users.map(user => syncUserToFirestore(user))
    );

    return NextResponse.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json();
    console.log('[API PUT] Received body:', JSON.stringify(body));
    const { uid, ...updateData } = body;

    if (!uid) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const auth = adminAuth; // Assuming adminAuth is your initialized Firebase Admin Auth instance

    // Prepare Auth updates
    const authUpdates: any = {};
    if (updateData.email) authUpdates.email = updateData.email;
    if (updateData.password) authUpdates.password = updateData.password;
    if (updateData.displayName) authUpdates.displayName = updateData.displayName;
    if (updateData.photoURL) authUpdates.photoURL = updateData.photoURL; // CRITICAL FIX: Add photoURL here

    console.log(`[API PUT] Updating Auth for UID: ${uid} with updates:`, JSON.stringify(authUpdates));
    if (Object.keys(authUpdates).length > 0) {
      await auth.updateUser(uid, authUpdates);
    }

    // Get the fully updated user record from Firebase Auth
    const updatedUserFromAuth = await auth.getUser(uid);
    console.log(`[API PUT] User from auth.getUser(${uid}) after Auth update:`, JSON.stringify(updatedUserFromAuth));
    
    // Sync the complete and updated Auth record to Firestore
    // This will now include the correct photoURL from Auth
    const finalUserDataInFirestore = await syncUserToFirestore(updatedUserFromAuth);

    return NextResponse.json({ success: true, user: finalUserDataInFirestore });

  } catch (error: any) { // Added :any to error for accessing error.code
    console.error('Error updating user:', error);
    if (error.code === 'auth/user-not-found') {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
}
