{"name": "adukrom-admin-new", "engines": {"node": "20.x"}, "version": "0.1.0", "private": true, "scripts": {"heroku-prep": "npm run build", "heroku-deploy": "git add . && git commit -m 'Heroku deploy' && git push heroku main", "dev": "next dev -p 3010", "build": "next build", "start": "next start", "lint": "next lint", "heroku-cache-clear": "heroku plugins:install heroku-repo && heroku repo:purge_cache -a adukingadmintest"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "firebase-functions": "^6.3.2", "firebase-functions-test": "^3.4.1", "framer-motion": "^12.18.1", "input-otp": "^1.4.2", "jsdom": "^26.1.0", "lucide-react": "^0.515.0", "next": "15.3.3", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "postcss": "^8.4.32", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/firebase": "^2.4.32", "@types/node": "^20.19.0", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.3", "firebase-tools": "^14.7.0", "typescript": "^5"}}