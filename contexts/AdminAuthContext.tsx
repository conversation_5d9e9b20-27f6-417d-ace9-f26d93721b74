'use client';

import { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { 
  User as FirebaseUser,
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  onAuthStateChanged,
  createUserWithEmailAndPassword,
  updateProfile
} from 'firebase/auth';
import { doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { UserRole } from '@/lib/types/role';

export interface AdminUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
  disabled: boolean;
  role: UserRole;
  createdAt?: any; // Can be Date or FieldValue
  lastSignInTime?: string;
  permissions?: string[];
}

interface AdminAuthContextType {
  user: AdminUser | null;
  loading: boolean;
  isAdmin: boolean;
  userRole: UserRole | null;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName: string) => Promise<AdminUser>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

export function useAdminAuth() {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
}

export function AdminAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const isAdmin = !!userRole && ['super_admin', 'admin'].includes(userRole);

  // Fetch user data from Firestore
  const fetchUserData = useCallback(async (firebaseUser: FirebaseUser) => {
    try {
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (!userDoc.exists()) {
        console.error('[Auth] User document not found in Firestore');
        return null;
      }
      
      const userData = userDoc.data();
      const role = userData.role as UserRole;
      
      // Get role permissions if needed
      let permissions: string[] = [];
      if (role) {
        const roleDoc = await getDoc(doc(db, 'roles', role));
        if (roleDoc.exists()) {
          permissions = roleDoc.data().permissions || [];
        }
      }

      const userWithRole: AdminUser = {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
        disabled: userData.disabled || false,
        role,
        permissions,
        createdAt: userData.createdAt,
        lastSignInTime: firebaseUser.metadata.lastSignInTime
      };

      return userWithRole;
    } catch (error) {
      console.error('[Auth] Error fetching user data:', error);
      return null;
    }
  }, []);

  // Check if user has required permission
  const hasPermission = useCallback((permission: string) => {
    if (!user) return false;
    // Super admin has all permissions
    if (user.role === 'super_admin') return true;
    // Check if user has the specific permission
    return user.permissions?.includes(permission) || false;
  }, [user]);

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      console.log('[Auth] Attempting to sign in with email:', email);
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      // Fetch user data from Firestore
      const userData = await fetchUserData(firebaseUser);
      
      if (!userData || !userData.role) {
        console.error('[Auth] User has no role assigned');
        await firebaseSignOut(auth);
        throw new Error('Access denied. No role assigned to user.');
      }
      
      // Only allow users with admin or super_admin roles to access the admin dashboard
      if (!['super_admin', 'admin', 'editor'].includes(userData.role)) {
        console.error('[Auth] User does not have admin access');
        await firebaseSignOut(auth);
        throw new Error('Access denied. Admin privileges required.');
      }
      
      setUser(userData);
      setUserRole(userData.role);
      
    } catch (error: any) {
      console.error('[Auth] Sign in error:', error);
      if (error.code === 'auth/network-request-failed') {
        throw new Error('Network error. Please check your internet connection and try again.');
      } else if (error.code === 'auth/wrong-password' || error.code === 'auth/user-not-found') {
        throw new Error('Invalid email or password.');
      } else if (error.code === 'auth/too-many-requests') {
        throw new Error('Too many failed attempts. Please try again later.');
      } else {
        throw new Error(error.message || 'Failed to sign in. Please try again.');
      }
    }
  };

  // Sign up new user
  const signUp = async (email: string, password: string, displayName: string) => {
    try {
      console.log('[Auth] Creating new user account:', email);
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      await updateProfile(userCredential.user, { displayName });

      const newUser: AdminUser = {
        uid: userCredential.user.uid,
        email,
        displayName,
        photoURL: null,
        emailVerified: false,
        disabled: false,
        role: 'editor', // Default role for new users
        createdAt: serverTimestamp(),
        lastSignInTime: userCredential.user.metadata.lastSignInTime
      };

      // Save user to Firestore
      await setDoc(doc(db, 'users', userCredential.user.uid), newUser);
      
      // Refresh user data
      const userData = await fetchUserData(userCredential.user);
      if (userData) {
        setUser(userData);
        setUserRole(userData.role);
      }
      
      return newUser;
    } catch (error: any) {
      console.error('[Auth] Sign up error:', error);
      if (error.code === 'auth/email-already-in-use') {
        throw new Error('An account with this email already exists.');
      } else if (error.code === 'auth/weak-password') {
        throw new Error('Password should be at least 6 characters.');
      } else {
        throw new Error(error.message || 'Failed to create account. Please try again.');
      }
    }
  };

  // Logout user
  const logout = async () => {
    try {
      await firebaseSignOut(auth);
      setUser(null);
      setUserRole(null);
    } catch (error) {
      console.error('[Auth] Logout error:', error);
      throw error;
    }
  };
  
  // Refresh user data
  const refreshUser = async () => {
    const currentUser = auth.currentUser;
    if (currentUser) {
      const userData = await fetchUserData(currentUser);
      if (userData) {
        setUser(userData);
        setUserRole(userData.role);
      }
    }
  };

  // Handle auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      try {
        if (firebaseUser) {
          console.log('[Auth] User state changed, fetching user data...');
          const userData = await fetchUserData(firebaseUser);
          
          if (userData && userData.role) {
            // Only allow users with admin/editor roles to access the admin dashboard
            if (['super_admin', 'admin', 'editor'].includes(userData.role)) {
              console.log('[Auth] User authenticated with role:', userData.role);
              setUser(userData);
              setUserRole(userData.role);
            } else {
              console.error('[Auth] User does not have admin access');
              await firebaseSignOut(auth);
              setUser(null);
              setUserRole(null);
            }
          } else {
            console.error('[Auth] User has no role assigned');
            await firebaseSignOut(auth);
            setUser(null);
            setUserRole(null);
          }
        } else {
          console.log('[Auth] No user signed in');
          setUser(null);
          setUserRole(null);
        }
      } catch (error) {
        console.error('[Auth] Error in auth state change:', error);
        setUser(null);
        setUserRole(null);
      } finally {
        setLoading(false);
      }
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [fetchUserData]);

  // Context value
  const value = {
    user,
    loading,
    isAdmin,
    userRole,
    signIn,
    signUp,
    logout,
    refreshUser,
    hasPermission
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {!loading && children}
    </AdminAuthContext.Provider>
  )
}
