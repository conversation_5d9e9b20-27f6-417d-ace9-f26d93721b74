# Ignore Firebase Storage directory
/firebase-storage/

# Ignore Firebase Storage rules and test files
/storage.rules
/scripts/upload-*.ts

# Ignore development and debugging files
.env.local
.env.development
.env.test

# Ignore IDE and editor files
.vscode/
.idea/
*.sublime-*

# Ignore OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Ignore test and coverage directories
/coverage
/__tests__
/*.test.*

# Ignore large directories that aren't needed in production
/node_modules/.cache/
/.next/cache/

# Ignore local development files
scripts/
firebase-debug.log*
firebase-debug.*.log*
