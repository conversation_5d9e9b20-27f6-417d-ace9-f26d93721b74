'use client'

import { useSidebar } from '@/hooks/use-sidebar'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Menu, Bell, Search, User, LogOut, Settings, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useState, useEffect } from 'react'
import { useAdminAuth } from '@/contexts/AdminAuthContext'


interface HeaderProps extends React.HTMLAttributes<HTMLElement> {}

export function Header({ className, ...props }: HeaderProps) {
  const { toggle, isOpen, isCollapsed } = useSidebar()
  const [isMobile, setIsMobile] = useState(false)
  const { user, logout } = useAdminAuth()

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  return (
    <header
      className={cn(
        "fixed top-0 right-0 z-40 border-b border-royalBlue-900/40 bg-gradient-to-l from-royalBlue-900 via-royalBlue-800 to-royalBlue-900 backdrop-blur-xl shadow-lg transition-all duration-300 ease-in-out text-white before:absolute before:inset-0 before:bg-white/10 before:rounded-b-xl before:pointer-events-none",
        // Adjust positioning based on sidebar state (matching main content layout)
        !isMobile && (isCollapsed ? 'left-16' : 'left-64'),
        isMobile && 'left-0',
        className
      )}
      {...props}
    >
      {/* Pattern overlay */}
      <div className="absolute inset-0 opacity-10 bg-[url('/images/pattern.svg')] pointer-events-none" />
      <div className="flex h-16 items-center px-4 sm:px-6 w-full text-white">
        {/* Mobile menu button */}
        <Button
          variant="ghost"
          size="icon"
          className="mr-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700 md:hidden"
          onClick={toggle}
          aria-label={isOpen ? 'Close sidebar' : 'Open sidebar'}
        >
          {isOpen ? (
            <X className="h-5 w-5" />
          ) : (
            <Menu className="h-5 w-5" />
          )}
          <span className="sr-only">{isOpen ? 'Close sidebar' : 'Open sidebar'}</span>
        </Button>

        {/* Search bar */}
        <div className="flex-1 max-w-xl ml-4">
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <Search className="h-4 w-4 text-ivory-400" />
            </div>
            <Input
              type="search"
              placeholder="Search..."
              className="w-full rounded-xl bg-ivory-50 pl-10 focus:bg-white focus:ring-2 focus:ring-ivory-300 border-ivory-200"
            />
          </div>
        </div>

        <div className="ml-auto flex items-center space-x-3">
          <Button 
            variant="ghost" 
            size="icon" 
            className="relative text-ivory-600 hover:bg-ivory-100 hover:text-ivory-900"
          >
            <Bell className="h-5 w-5" />
            <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-yellow-500"></span>
            <span className="sr-only">View notifications</span>
          </Button>
          
          {/* Profile dropdown */}
          <div className="flex items-center space-x-3">
            <span className="hidden sm:block text-xs text-gray-200 truncate max-w-xs">
              {user?.email || '<EMAIL>'}
            </span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="flex items-center space-x-2 focus:outline-none">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user?.photoURL || undefined} alt={user?.email || ''} />
                    <AvatarFallback>{user?.email ? user.email[0].toUpperCase() : 'A'}</AvatarFallback>
                  </Avatar>
                  <span className="sr-only">Open user menu</span>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48 bg-white text-black border border-gray-200 shadow-lg rounded-md dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700">
                <DropdownMenuLabel>Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {/* TODO: Route to user profile page when created */}
                <DropdownMenuItem className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  className="cursor-pointer text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-500 dark:hover:bg-red-900/50 dark:hover:text-red-400"
                  onClick={async () => {
                    if (logout) {
                      await logout();
                      window.location.href = '/login'; // Redirect to login page
                    }
                  }}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  )
}
