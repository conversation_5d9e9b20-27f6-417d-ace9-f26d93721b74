import { DecoratorN<PERSON>, <PERSON>cal<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SerializedLexicalNode, Spread } from "lexical";
import * as React from "react";

export type SerializedHorizontalRuleNode = Spread<
  {
    type: "horizontalrule";
    version: 1;
  },
  SerializedLexicalNode
>;

export class HorizontalRuleNode extends DecoratorNode<JSX.Element> {
  static getType(): string {
    return "horizontalrule";
  }

  static clone(node: HorizontalRuleNode): HorizontalRuleNode {
    return new HorizontalRuleNode(node.__key);
  }

  createDOM(): HTMLElement {
    const dom = document.createElement("hr");
    dom.className = "my-4 border-t border-white/30";
    return dom;
  }

  updateDOM(): false {
    return false;
  }

  decorate(): JSX.Element {
    return React.createElement("hr", { className: "my-4 border-t border-white/30" });
  }

  static importJSON(): HorizontalRuleNode {
    return new HorizontalRuleNode();
  }

  exportJSON(): SerializedHorizontalRuleNode {
    return {
      type: "horizontalrule",
      version: 1,
    };
  }
}

export function $createHorizontalRuleNode(): HorizontalRuleNode {
  return new HorizontalRuleNode();
}

export function $isHorizontalRuleNode(node: unknown): node is HorizontalRuleNode {
  return node instanceof HorizontalRuleNode;
}
