"use client";
import React, { useRef, useEffect, useState } from "react";
import { storage } from "@/lib/firebase";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { Loader2 } from "lucide-react";

interface SimpleRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const BUTTONS = [
  { cmd: "bold", icon: "B", title: "Bold" },
  { cmd: "italic", icon: "I", title: "Italic" },
  { cmd: "underline", icon: "U", title: "Underline" },
  { cmd: "strikeThrough", icon: "S", title: "Strikethrough" },
  { cmd: "foreColor", icon: "A", color: true, title: "Text Color" },
  { cmd: "hiliteColor", icon: "🖍️", color: true, highlight: true, title: "Highlight" },
  { cmd: "link", icon: "🔗", title: "Link" },
  { cmd: "formatBlock", arg: "H1", icon: "H1", title: "Heading 1" },
  { cmd: "formatBlock", arg: "H2", icon: "H2", title: "Heading 2" },

  { cmd: "formatBlock", arg: "PRE", icon: "</>", title: "Code" },
  { cmd: "insertImage", icon: "🖼️", title: "Image" },
  { cmd: "insertHorizontalRule", icon: "―", title: "Horizontal Rule" },
  { cmd: "insertUnorderedList", icon: "• List", title: "Bullet List" },
  { cmd: "insertOrderedList", icon: "1. List", title: "Numbered List" },
  { cmd: "undo", icon: "↺", title: "Undo" },
  { cmd: "redo", icon: "↻", title: "Redo" },
  { cmd: "removeFormat", icon: "⎚", title: "Clear Formatting" },
  { cmd: "insertTable", icon: "▦", title: "Table" },
];

export default function SimpleRichTextEditor({ value, onChange, placeholder, className }: SimpleRichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value || "";
    }
  }, [value]);

  function handleInput() {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  }

  const [uploading, setUploading] = useState(false);

  // Create a hidden file input for image uploads
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle image upload
  async function handleImageUpload(file: File) {
    if (!file) return;
    
    setUploading(true);
    
    try {
      // Create a reference to the file in Firebase Storage
      const fileRef = ref(storage, `news/content/${Date.now()}_${file.name}`);
      
      // Upload the file
      await uploadBytes(fileRef, file);
      
      // Get the download URL
      const url = await getDownloadURL(fileRef);
      
      // Insert the image at cursor position
      if (editorRef.current) {
        document.execCommand("insertImage", false, url);
      }
      
      console.log('Content image uploaded successfully');
    } catch (err: any) {
      console.error('Error uploading content image:', err);
      alert('Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
    }
  }

  function handleCommand(cmd: string, arg?: string) {
    if (cmd === "insertImage") {
      // Open file selector instead of prompting for URL
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    } else if (cmd === "formatBlock") {
      let blockArg = arg;

      if (arg === "PRE") blockArg = "<pre>";
      document.execCommand("formatBlock", false, blockArg);
    } else if (cmd === "foreColor" || cmd === "hiliteColor") {
      const color = window.prompt(cmd === "foreColor" ? "Text color (CSS or hex):" : "Highlight color (CSS or hex):", cmd === "foreColor" ? "#007aff" : "#ffff00");
      if (color) document.execCommand(cmd, false, color);
    } else if (cmd === "strikeThrough") {
      document.execCommand("strikeThrough", false, undefined);
    } else if (cmd === "link") {
      const url = window.prompt("Link URL:");
      if (url) document.execCommand("createLink", false, url);
    } else if (cmd === "insertHorizontalRule") {
      document.execCommand("insertHorizontalRule");
    } else if (cmd === "undo" || cmd === "redo" || cmd === "removeFormat") {
      document.execCommand(cmd);
    } else if (cmd === "insertTable") {
      // Basic 2x2 table
      if (editorRef.current) {
        const table = document.createElement('table');
        table.style.border = '1px solid #fff';
        table.style.width = '100%';
        for (let i = 0; i < 2; i++) {
          const tr = document.createElement('tr');
          for (let j = 0; j < 2; j++) {
            const td = document.createElement('td');
            td.innerHTML = '&nbsp;';
            td.style.border = '1px solid #fff';
            td.style.padding = '4px';
            tr.appendChild(td);
          }
          table.appendChild(tr);
        }
        document.execCommand('insertHTML', false, table.outerHTML);
      }
    } else {
      document.execCommand(cmd, false, arg);
    }
    handleInput();
  }

  return (
    <div className={`simple-rte-container ${className || ""}`}>
      <style jsx>{`
        .simple-rte-container img {
          max-width: 100%;
          height: auto;
          display: block;
          margin: 1rem 0;
        }
        .simple-rte-container iframe {
          max-width: 100%;
          margin: 1rem 0;
        }
      `}</style>
      {/* Hidden file input for image uploads */}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            handleImageUpload(file);
          }
          // Reset the input so the same file can be selected again
          e.target.value = '';
        }}
      />
      <div className="simple-rte-toolbar">
        {uploading && (
          <div className="uploading-indicator">
            <Loader2 className="animate-spin" size={16} />
            <span>Uploading image...</span>
          </div>
        )}
        {BUTTONS.map(btn => (
          btn.color ? (
            <button
              key={btn.icon + btn.cmd + (btn.arg || "")}
              type="button"
              title={btn.title}
              onMouseDown={e => {
                e.preventDefault();
                handleCommand(btn.cmd, btn.arg);
              }}
              className="simple-rte-btn"
              style={{ color: btn.cmd === "foreColor" ? "#007aff" : btn.cmd === "hiliteColor" ? "#f7e600" : undefined }}
            >
              {btn.icon}
            </button>
          ) : (
            <button
              key={btn.icon + btn.cmd + (btn.arg || "")}
              type="button"
              title={btn.title}
              onMouseDown={e => {
                e.preventDefault();
                handleCommand(btn.cmd, btn.arg);
              }}
              className="simple-rte-btn"
            >
              {btn.icon}
            </button>
          )
        ))}
      </div>
      <div
        ref={editorRef}
        className="simple-rte-editor"
        contentEditable
        suppressContentEditableWarning
        data-placeholder={placeholder}
        onInput={handleInput}
        style={{ minHeight: 150, outline: "none", background: "rgba(30,40,60,0.7)", color: "#fff", borderRadius: 8, padding: 12, marginTop: 4 }}
      />
      <style jsx>{`
        .simple-rte-container { position: relative; }
        .simple-rte-toolbar {
          display: flex;
          gap: 8px;
          background: rgba(30,40,60,0.85);
          border-radius: 8px;
          padding: 6px 8px;
          margin-bottom: 4px;
          position: relative;
        }
        .uploading-indicator {
          position: absolute;
          right: 8px;
          top: 6px;
          display: flex;
          align-items: center;
          gap: 6px;
          background: rgba(0,0,0,0.6);
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          color: white;
        }
        .simple-rte-btn {
          background: none;
          border: none;
          color: #fff;
          font-weight: bold;
          font-size: 1rem;
          cursor: pointer;
          border-radius: 4px;
          padding: 4px 8px;
          transition: background 0.2s;
        }
        .simple-rte-btn:hover {
          background: rgba(80,120,200,0.25);
        }
        .simple-rte-editor:empty:before {
          content: attr(data-placeholder);
          color: #bbb;
          pointer-events: none;
        }
      `}</style>
    </div>
  );
}
