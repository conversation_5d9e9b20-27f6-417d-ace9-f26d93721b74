import { <PERSON>ement<PERSON><PERSON>, Lexical<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SerializedElementNode, Spread } from "lexical";

export type SerializedHeadingNode = Spread<
  {
    type: "heading";
    tag: string;
    version: 1;
  },
  SerializedElementNode
>;

export class HeadingNode extends ElementNode {
  static getType(): string {
    return "heading";
  }
  static clone(node: HeadingNode): HeadingNode {
    return new HeadingNode(node.__tag, node.__key);
  }
  static nodeType = "heading";
  static get className() {
    return "HeadingNode";
  }
  __tag: string;

  constructor(tag = "h1", key?: NodeKey) {
    super(key);
    this.__tag = tag;
  }

  createDOM(): HTMLElement {
    return document.createElement(this.__tag);
  }

  updateDOM(prevNode: HeadingNode, dom: HTMLElement): boolean {
    if (dom.tagName.toLowerCase() !== this.__tag) {
      const newDom = document.createElement(this.__tag);
      dom.replaceWith(newDom);
      return true;
    }
    return false;
  }

  static importJSON(serializedNode: SerializedHeadingNode): HeadingNode {
    const node = new HeadingNode(serializedNode.tag);
    node.setFormat(serializedNode.format);
    node.setIndent(serializedNode.indent);
    node.setDirection(serializedNode.direction);
    return node;
  }

  exportJSON(): SerializedHeadingNode {
    return {
      ...super.exportJSON(),
      type: "heading",
      tag: this.__tag,
      version: 1,
    };
  }
}

export function $createHeadingNode(tag: string): HeadingNode {
  return new HeadingNode(tag);
}

export function $isHeadingNode(node: unknown): node is HeadingNode {
  return node instanceof HeadingNode;
}
