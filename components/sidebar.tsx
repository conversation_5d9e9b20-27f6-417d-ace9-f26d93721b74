'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useSidebar } from '@/hooks/use-sidebar'
import { Button } from '@/components/ui/button'
import { 
  Home, 
  Newspaper, 
  Image as ImageIcon, 
  Calendar, 
  Users, 
  Settings,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  Handshake,
  MailCheck
} from 'lucide-react'
import { useState, useEffect } from 'react'

type NavItem = {
  name: string
  href: string
  icon: React.ReactNode
  activeIcon?: React.ReactNode
}

const navigation: NavItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: <Home className="h-5 w-5" />,
    activeIcon: <Home className="h-5 w-5 text-primary" />
  },
  {
    name: 'News',
    href: '/dashboard/news',
    icon: <Newspaper className="h-5 w-5" />,
    activeIcon: <Newspaper className="h-5 w-5 text-primary" />
  },
  {
    name: 'Events',
    href: '/dashboard/events',
    icon: <Calendar className="h-5 w-5" />,
    activeIcon: <Calendar className="h-5 w-5 text-primary" />
  },
  {
    name: 'Gallery',
    href: '/dashboard/gallery',
    icon: <ImageIcon className="h-5 w-5" />,
    activeIcon: <ImageIcon className="h-5 w-5 text-primary" />
  },
  {
    name: 'Partners',
    href: '/dashboard/partners',
    icon: <Handshake className="h-5 w-5" />,
    activeIcon: <Handshake className="h-5 w-5 text-primary" />
  },
  {
    name: 'Users',
    href: '/dashboard/users',
    icon: <Users className="h-5 w-5" />,
    activeIcon: <Users className="h-5 w-5 text-primary" />
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: <Settings className="h-5 w-5" />,
    activeIcon: <Settings className="h-5 w-5 text-primary" />
  },
  {
    name: 'RSVPs',
    href: '/dashboard/rsvp',
    icon: <MailCheck className="h-5 w-5" />,
    activeIcon: <MailCheck className="h-5 w-5 text-primary" />
  }
]

import { useAdminAuth } from '@/contexts/AdminAuthContext'

export function Sidebar() {
  const pathname = usePathname()
  const { isOpen, setOpen, isCollapsed, toggleCollapsed } = useSidebar()
  const [isMobile, setIsMobile] = useState(false)
  const { user } = useAdminAuth()

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)

    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  // Toggle sidebar collapse state (only on desktop)
  const handleToggleCollapse = () => {
    if (!isMobile) {
      toggleCollapsed();
    }
  };

  // Close sidebar on mobile when a nav item is clicked
  const handleNavClick = () => {
    if (isMobile) {
      setOpen(false);
    }
  };

  return (
    <>
      {(!isMobile || isOpen) && (
        <div className={cn(
          "flex h-full flex-col bg-white transition-transform duration-300",
          isMobile ? (isOpen ? 'w-64 min-w-[16rem] translate-x-0' : '-translate-x-full w-64 min-w-[16rem]') : 'w-full',
          isMobile && 'fixed left-0 top-0 z-50 h-screen',
        )}>
    

        {/* Sidebar header */}
        <div className={cn(
          "flex items-center justify-between h-16 border-b border-royalBlue-900/40 bg-gradient-to-r from-royalBlue-900 via-royalBlue-800 to-teal-400 relative shadow-lg text-white backdrop-blur-xl before:absolute before:inset-0 before:bg-white/10 before:rounded-b-xl before:pointer-events-none",
          isCollapsed ? 'px-3' : 'px-4'
        )}>
          {/* Pattern overlay */}
          <div className="absolute inset-0 opacity-10 bg-[url('/images/pattern.svg')] pointer-events-none" />
          {!isCollapsed && (
            <h1 className="text-2xl font-extrabold whitespace-nowrap text-white drop-shadow-md flex items-center">
              Adukrom Admin
            </h1>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => {
              if (isMobile) {
                setOpen(false);
              } else {
                handleToggleCollapse();
              }
            }}
            aria-label={isCollapsed ? (isMobile ? 'Close sidebar' : 'Expand sidebar') : (isMobile ? 'Close sidebar' : 'Collapse sidebar')}
          >
            {isCollapsed ? (
              <ChevronRight className="h-5 w-5" />
            ) : (
              <ChevronLeft className="h-5 w-5" />
            )}
          </Button>
        </div>
        
        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto py-4 px-2 bg-gradient-to-b from-royalBlue-900 via-royalBlue-800 to-forestGreen-900 relative text-white backdrop-blur-xl before:absolute before:inset-0 before:bg-white/10 before:rounded-b-xl before:pointer-events-none">
          <div className="space-y-1 relative">
            {navigation.map((item) => {
              const isActive = pathname ? 
                (pathname.startsWith(item.href) || 
                // Handle root dashboard path
                (pathname === '/dashboard' && item.href === '/dashboard') ||
                // Handle index routes
                (pathname === `${item.href}/` || `${pathname}/` === item.href)) : false;
              
              const Icon = isActive ? (item.activeIcon || item.icon) : item.icon
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                    isActive
                      ? 'bg-white/10 text-white font-semibold'
                      : 'text-gray-300 hover:bg-white/5 hover:text-white',
                    isCollapsed ? 'justify-center' : 'px-4'
                  )}
                  onClick={() => {
                    // Close mobile sidebar when an item is clicked
                    if (isMobile) {
                      setOpen(false);
                    }
                  }}
                >
                  <span className={cn(
                    'flex items-center justify-center',
                    isCollapsed ? 'w-6' : isMobile ? 'w-8 mr-4' : 'w-6 mr-3',
                    isMobile && 'text-xl'
                  )}>
                    {Icon}
                  </span>
                  {(!isCollapsed || isMobile) && (
                    <span className={cn(
                      isMobile ? 'text-base font-semibold' : ''
                    )}>{item.name}</span>
                  )}
                </Link>
              )
            })}
          </div>
        </nav>
        
        {/* User profile */}
        <div className={cn(
          'border-t border-gray-200 dark:border-gray-800 p-4',
          isCollapsed ? 'px-2' : 'px-4'
        )}>
          <div className={cn(
            'flex items-center',
            isCollapsed ? 'justify-center' : 'justify-between'
          )}>
            {!isCollapsed && (
              <div className="flex items-center min-w-0">
                <div className="h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <span className="text-primary font-medium">AD</span>
                </div>
                <div className="ml-3 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    Admin User
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {user?.email || '<EMAIL>'}
                  </p>
                </div>
              </div>
            )}
            {isCollapsed && (
              <div className="h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                <span className="text-primary font-medium text-sm">AD</span>
              </div>
            )}
          </div>
        </div>
        </div>
      )}
    </>
  )
}
