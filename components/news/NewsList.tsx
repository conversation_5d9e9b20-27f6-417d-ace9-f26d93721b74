'use client';

import { useEffect, useState } from 'react';
import type { News } from '@/types/firestore';
import { db } from '@/lib/firebase';
import { collection, query, orderBy, onSnapshot, deleteDoc, doc } from 'firebase/firestore';
import Link from 'next/link';
import { Calendar, Edit, Trash2 } from 'lucide-react';

export default function NewsList() {
  const [news, setNews] = useState<News[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const q = query(collection(db, 'news'), orderBy('publishedAt', 'desc'));
    const unsub = onSnapshot(q, (snapshot) => {
      const docs = snapshot.docs.map((doc) => {
        const data = doc.data();
        const mapped: News = {
          id: doc.id,
          title: typeof data.title === 'string' ? data.title : '',
          excerpt: typeof data.excerpt === 'string' ? data.excerpt : '',
          content: typeof data.content === 'string' ? data.content : '',
          publishedAt: typeof data.publishedAt === 'string' ? data.publishedAt : '',
          category: typeof data.category === 'string' ? data.category : '',
          categoryName: typeof data.categoryName === 'string' ? data.categoryName : '',
          mainImage: typeof data.mainImage === 'string' ? data.mainImage : '',
          status: typeof data.status === 'string' ? data.status : '',
          readTime: typeof data.readTime === 'string' ? data.readTime : '',
          featured: typeof data.featured === 'boolean' ? data.featured : false,
        };
        if (!mapped.title || !mapped.content) return null;
        for (const key of Object.keys(mapped)) {
          const v = (mapped as any)[key];
          if ((typeof v === 'object' && v !== null && !Array.isArray(v)) || Array.isArray(v)) return null;
        }
        return mapped;
      }).filter(Boolean) as News[];
      setNews(docs);
      setLoading(false);
    });
    return () => unsub();
  }, []);

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this article?')) {
      await deleteDoc(doc(db, 'news', id));
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (e) {
      return '-';
    }
  };

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-extrabold text-royalBlue">News Management</h1>
        <Link 
          href="/admin/news/create" 
          className="bg-royalBlue hover:bg-royalGold text-white hover:text-royalBlue px-5 py-2.5 rounded-lg font-semibold shadow transition-all duration-200 flex items-center gap-2"
        >
          <span>+ Create Article</span>
        </Link>
      </div>
      
      <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100">
        <div className="grid grid-cols-11 items-center px-6 py-4 text-xs uppercase font-bold text-gray-500 tracking-wider bg-gray-50">
          <div className="col-span-2">Image</div>
          <div className="col-span-3">Title</div>
          <div className="col-span-2">Category</div>
          <div className="col-span-2">Published</div>
          <div className="col-span-2 text-right">Actions</div>
        </div>
        
        {loading ? (
          <div className="px-6 py-16 text-center">
            <div className="animate-pulse flex flex-col items-center">
              <div className="h-8 w-8 bg-royalBlue/20 rounded-full mb-4"></div>
              <div className="h-4 w-32 bg-royalBlue/20 rounded mb-2"></div>
              <div className="h-3 w-48 bg-royalBlue/10 rounded"></div>
            </div>
          </div>
        ) : news.length === 0 ? (
          <div className="px-6 py-16 text-center">
            <div className="flex flex-col items-center">
              <div className="h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Calendar className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-700 mb-1">No articles yet</h3>
              <p className="text-gray-500 mb-6">Create your first news article to get started</p>
              <Link 
                href="/admin/news/create" 
                className="bg-royalBlue hover:bg-royalGold text-white hover:text-royalBlue px-4 py-2 rounded-lg font-medium text-sm shadow transition-all duration-200"
              >
                Create Article
              </Link>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {news.map((item) => (
              <div key={item.id} className="grid grid-cols-11 items-center px-6 py-4 hover:bg-gray-50 transition-colors duration-150">
                <div className="col-span-2">
                  {item.mainImage ? (
                    <img 
                      src={item.mainImage} 
                      alt={item.title} 
                      className="h-16 w-24 object-cover rounded-lg border border-gray-200 shadow-sm" 
                    />
                  ) : (
                    <div className="h-16 w-24 bg-gray-100 rounded-lg flex items-center justify-center text-gray-300 font-bold text-sm">
                      No Image
                    </div>
                  )}
                </div>
                <div className="col-span-3">
                  <h3 className="font-semibold text-base text-royalBlue truncate mb-1" title={item.title}>
                    {item.title}
                  </h3>
                  <p className="text-xs text-gray-500 truncate" title={item.excerpt}>
                    {item.excerpt || 'No excerpt available'}
                  </p>
                </div>
                <div className="col-span-2">
                  {item.category || item.categoryName ? (
                    <span className="px-2.5 py-1 bg-royalBlue/10 text-royalBlue rounded-full text-xs font-medium">
                      {item.category || item.categoryName}
                    </span>
                  ) : (
                    <span className="text-gray-400 text-sm">-</span>
                  )}
                </div>
                <div className="col-span-2 text-sm text-gray-600 flex items-center gap-1.5">
                  <Calendar className="h-3.5 w-3.5 text-gray-400" />
                  <span>{item.publishedAt ? formatDate(item.publishedAt) : '-'}</span>
                </div>
                <div className="col-span-2 flex justify-end gap-2">
                  <Link 
                    href={`/admin/news/${item.id}/edit`} 
                    className="p-2 rounded-lg bg-royalBlue/10 text-royalBlue hover:bg-royalBlue hover:text-white transition-colors duration-200"
                    title="Edit article"
                  >
                    <Edit className="h-4 w-4" />
                  </Link>
                  <button 
                    onClick={() => handleDelete(item.id)} 
                    className="p-2 rounded-lg bg-red-50 text-red-500 hover:bg-red-500 hover:text-white transition-colors duration-200"
                    title="Delete article"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
