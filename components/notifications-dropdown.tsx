'use client';

import { Bell, Check, Clock, AlertCircle, Image as ImageIcon, Users, Settings as SettingsIcon, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useNotifications } from '@/contexts/NotificationContext';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

export function NotificationsDropdown() {
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'event':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'news':
        return <AlertCircle className="h-4 w-4 text-green-500" />;
      case 'gallery':
        return <ImageIcon className="h-4 w-4 text-purple-500" />;
      case 'partner':
        return <Users className="h-4 w-4 text-amber-500" />;
      default:
        return <SettingsIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className="relative text-ivory-600 hover:bg-ivory-100 hover:text-ivory-900"
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
          <span className="sr-only">View notifications</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        className="w-80 p-0 border border-gray-200 shadow-lg rounded-lg overflow-hidden"
        align="end"
      >
        <DropdownMenuLabel className="px-4 py-3 border-b border-gray-100 bg-gray-50 flex justify-between items-center">
          <div className="flex items-center">
            <Bell className="h-4 w-4 mr-2" />
            <span>Notifications</span>
          </div>
          {notifications.length > 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-6 px-2 text-xs text-blue-600 hover:bg-blue-50"
              onClick={(e) => {
                e.stopPropagation();
                markAllAsRead();
              }}
            >
              Mark all as read
            </Button>
          )}
        </DropdownMenuLabel>
        
        <div className="max-h-[400px] overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="p-4 text-center text-sm text-gray-500">
              No notifications yet
            </div>
          ) : (
            notifications.map((notification) => (
              <DropdownMenuItem 
                key={notification.id} 
                className={cn(
                  "flex items-start p-3 border-b border-gray-100 hover:bg-gray-50 transition-colors",
                  !notification.read && "bg-blue-50"
                )}
                onClick={() => {
                  if (!notification.read) markAsRead(notification.id);
                  if (notification.link) {
                    window.location.href = notification.link;
                  }
                }}
              >
                <div className="flex-shrink-0 mr-3 mt-0.5">
                  <Avatar className="h-8 w-8 bg-white border border-gray-200">
                    <AvatarFallback className="bg-transparent">
                      {getNotificationIcon(notification.type)}
                    </AvatarFallback>
                  </Avatar>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {notification.title}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {notification.message}
                  </p>
                  <div className="flex items-center mt-1 text-xs text-gray-400">
                    {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true })}
                    {!notification.read && (
                      <span className="ml-2 inline-block h-2 w-2 rounded-full bg-blue-500"></span>
                    )}
                  </div>
                </div>
                <button
                  className="ml-2 text-gray-400 hover:text-gray-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    markAsRead(notification.id);
                  }}
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </DropdownMenuItem>
            ))
          )}
        </div>
        
        {notifications.length > 0 && (
          <div className="p-2 border-t border-gray-100 bg-gray-50 text-center">
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-xs text-gray-600 hover:text-gray-900"
              onClick={() => {
                // Clear all notifications or navigate to notifications page
              }}
            >
              View all notifications
            </Button>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
