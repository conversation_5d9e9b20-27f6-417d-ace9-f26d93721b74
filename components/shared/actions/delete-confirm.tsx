'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface DeleteConfirmProps {
  id: string;
  title: string;
  description: string;
  onConfirm: (id: string) => Promise<boolean>;
  onSuccess?: () => void;
  trigger: React.ReactNode;
}

export function DeleteConfirm({
  id,
  title,
  description,
  onConfirm,
  onSuccess,
  trigger,
}: DeleteConfirmProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleDelete = async () => {
    try {
      setIsLoading(true);
      const success = await onConfirm(id);
      
      if (success) {
        toast({
          title: 'Success',
          description: `${title} deleted successfully`,
        });
        
        setOpen(false);
        
        if (onSuccess) {
          onSuccess();
        } else {
          router.refresh();
        }
      } else {
        toast({
          title: 'Error',
          description: `Failed to delete ${title.toLowerCase()}. Please try again.`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      toast({
        title: 'Error',
        description: `An unexpected error occurred while deleting ${title.toLowerCase()}.`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        setOpen(true);
      }}>
        {trigger}
      </div>
      
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {title}?</AlertDialogTitle>
            <AlertDialogDescription>
              {description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete} 
              disabled={isLoading}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isLoading ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export default DeleteConfirm;
