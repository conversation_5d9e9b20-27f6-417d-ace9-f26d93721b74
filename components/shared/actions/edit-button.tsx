'use client';

import { Pencil } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface EditButtonProps {
  href: string;
  label?: string;
  className?: string;
  variant?: 'ghost' | 'outline' | 'link' | 'default' | 'destructive' | 'secondary';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
}

export function EditButton({
  href,
  label = 'Edit',
  className = '',
  variant = 'ghost',
  size = 'icon',
  disabled = false,
}: EditButtonProps) {
  return (
    <Button
      asChild
      variant={variant}
      size={size}
      className={className}
      onClick={(e) => e.stopPropagation()}
      disabled={disabled}
    >
      <Link href={href} className="flex items-center gap-2">
        <Pencil className="h-4 w-4" />
        {size !== 'icon' && <span>{label}</span>}
      </Link>
    </Button>
  );
}

export default EditButton;
