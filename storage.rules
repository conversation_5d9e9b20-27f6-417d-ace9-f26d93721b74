rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Development mode - allow all access
    match /{allPaths=**} {
      allow read, write: if true;
    }
    
    // Production rules - commented out for now
    /*
    // Allow read access to all files
    match /{allPaths=**} {
      allow read: if true;
    }
    
    // Allow authenticated users to write to the news directory
    match /news/{imageId} {
      allow write: if request.auth != null;
      allow read: if true;
    }
    
    // Allow authenticated users to write to the news/content directory for editor images
    match /news/content/{imageId} {
      allow write: if request.auth != null;
      allow read: if true;
    }
    
    // Allow authenticated users to write to the gallery directory
    match /gallery/{imageId} {
      allow write: if request.auth != null && 
        request.auth.token.role in ['admin', 'super_admin'] &&
        request.resource.contentType.matches('image/.*');
      allow read: if true;
    }

    // Allow authenticated users to write to the user-avatars directory
    match /user-avatars/{imageId} {
      allow write: if request.auth != null;
      allow read: if true;
    }
    */
      }
      
      match /partners/{imageId} {
        allow write: if request.auth != null && 
          request.auth.token.role in ['admin', 'super_admin'] &&
          request.resource.contentType.matches('image/.*');
      }
      
      // Default deny all other writes
      allow write: if false;
    }
    */
  }
}