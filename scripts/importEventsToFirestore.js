// Usage: node scripts/importEventsToFirestore.js
// Imports event.cleaned.json into the Firestore 'events' collection (plural, as expected by the UI)
// Only adds/updates docs; does NOT delete anything from Firestore.
// Requires service-account-key.json in project root.

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

const serviceAccount = require('../service-account-key.json');

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

const db = admin.firestore();

const file = 'event.cleaned.json';
const collection = 'events'; // PLURAL!

async function importEvents() {
  const filePath = path.join(__dirname, '../', file);
  if (!fs.existsSync(filePath)) {
    console.warn(`File not found: ${file}`);
    return;
  }
  const raw = fs.readFileSync(filePath, 'utf-8');
  let data;
  try {
    data = JSON.parse(raw);
  } catch (e) {
    console.error(`Failed to parse ${file}:`, e);
    return;
  }
  if (!Array.isArray(data)) {
    console.warn(`Expected array in ${file}, got ${typeof data}`);
    return;
  }
  const batch = db.batch();
  data.forEach(doc => {
    if (!doc.id) {
      console.warn(`Skipping doc without 'id' in ${file}`);
      return;
    }
    const ref = db.collection(collection).doc(doc.id);
    batch.set(ref, doc, { merge: true });
  });
  await batch.commit();
  console.log(`Imported ${data.length} docs to ${collection}`);
}

importEvents().then(() => {
  console.log('Events import complete!');
  process.exit(0);
});
