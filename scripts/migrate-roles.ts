import { initializeApp, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import admin from 'firebase-admin';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Firebase Admin
const serviceAccount = JSON.parse(
  Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || '', 'base64').toString('utf-8')
);

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = getFirestore();

async function migrateRoles() {
  try {
    console.log('Starting roles migration...');
    const batch = db.batch();
    
    // 1. Create default roles
    const rolesRef = db.collection('roles');
    const defaultRoles = [
      {
        id: 'super_admin',
        name: 'Super Admin',
        description: 'Full access to all features',
        permissions: ['*'],
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'admin',
        name: 'Administrator',
        description: 'Can manage content and users',
        permissions: ['manage:users', 'manage:content'],
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'editor',
        name: 'Editor',
        description: 'Can create and edit content',
        permissions: ['create:content', 'edit:content'],
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'user',
        name: 'User',
        description: 'Regular user with basic access',
        permissions: ['view:content'],
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      }
    ];

    for (const role of defaultRoles) {
      const roleRef = rolesRef.doc(role.id);
      const roleDoc = await roleRef.get();
      if (!roleDoc.exists) {
        batch.set(roleRef, role);
        console.log(`Added role: ${role.id}`);
      } else {
        console.log(`Role ${role.id} already exists, skipping...`);
      }
    }

    // 2. Migrate admin users
    const adminUsers = await db.collection('adminUser').get();
    for (const doc of adminUsers.docs) {
      const userData = doc.data();
      const userRef = db.collection('users').doc(doc.id);
      const userDoc = await userRef.get();
      
      if (!userDoc.exists) {
        batch.set(userRef, {
          ...userData,
          role: 'admin', // Default to admin role for existing admin users
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          createdAt: userData.createdAt || admin.firestore.FieldValue.serverTimestamp()
        });
        console.log(`Migrated admin user: ${doc.id}`);
      } else {
        console.log(`User ${doc.id} already exists in users collection, skipping...`);
      }
    }

    await batch.commit();
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

migrateRoles();
