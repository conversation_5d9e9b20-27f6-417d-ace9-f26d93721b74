import * as admin from 'firebase-admin';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Decode the base64 service account key
const serviceAccount = JSON.parse(
  Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || '', 'base64').toString('utf-8')
);

// Initialize Firebase Admin with the decoded service account
admin.initializeApp({
  credential: admin.credential.cert({
    projectId: serviceAccount.project_id,
    clientEmail: serviceAccount.client_email,
    privateKey: serviceAccount.private_key,
  } as admin.ServiceAccount),
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET
});

const bucket = admin.storage().bucket();

async function uploadLogo() {
  try {
    const logoPath = path.join(process.cwd(), 'public', 'images', 'ai-logo1a.png');
    const destination = 'images/ai-logo1a.png';
    
    console.log('Uploading logo to Firebase Storage...');
    
    // Upload the file
    await bucket.upload(logoPath, {
      destination,
      metadata: {
        contentType: 'image/png',
        cacheControl: 'public, max-age=********', // Cache for 1 year
      },
    });
    
    // Make the file public
    const file = bucket.file(destination);
    await file.makePublic();
    
    // Get the public URL
    const [metadata] = await file.getMetadata();
    const url = `https://storage.googleapis.com/${metadata.bucket}/${metadata.name}`;
    
    console.log('Logo uploaded successfully!');
    console.log('Public URL:', url);
    
    return url;
  } catch (error) {
    console.error('Error uploading logo:', error);
    throw error;
  }
}

// Run the upload
uploadLogo()
  .then(() => process.exit(0))
  .catch(() => process.exit(1));
