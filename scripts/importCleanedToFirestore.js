// Usage: node scripts/importCleanedToFirestore.js
// Imports all *.cleaned.json files into Firestore collections.
// Only adds/updates docs; does NOT delete anything from Firestore.
// Requires service-account-key.json in project root.

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

const serviceAccount = require('../service-account-key.json');

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

const db = admin.firestore();

// List your cleaned files and their target collections
const imports = [
  { file: 'news.cleaned.json', collection: 'news' },
  { file: 'event.cleaned.json', collection: 'event' },
  { file: 'gallery.cleaned.json', collection: 'gallery' },
  { file: 'strategicPartner.cleaned.json', collection: 'strategicPartner' },
  { file: 'users.cleaned.json', collection: 'users' },
  { file: 'adminUser.cleaned.json', collection: 'adminUser' },
  { file: 'category.cleaned.json', collection: 'category' },
  { file: 'newsletter.cleaned.json', collection: 'newsletter' },
  { file: 'page.cleaned.json', collection: 'page' },
  { file: 'pageView.cleaned.json', collection: 'pageView' },
  { file: 'post.cleaned.json', collection: 'post' },
  { file: 'rsvp.cleaned.json', collection: 'rsvp' },
  { file: 'siteSettings.cleaned.json', collection: 'siteSettings' },
  { file: 'storeCategory.cleaned.json', collection: 'storeCategory' },
  { file: 'websiteAssets.cleaned.json', collection: 'websiteAssets' },
  { file: 'websiteImages.cleaned.json', collection: 'websiteImages' },
];

async function importCollection({ file, collection }) {
  const filePath = path.join(__dirname, '../', file);
  if (!fs.existsSync(filePath)) {
    console.warn(`File not found: ${file}`);
    return;
  }
  const raw = fs.readFileSync(filePath, 'utf-8');
  let data;
  try {
    data = JSON.parse(raw);
  } catch (e) {
    console.error(`Failed to parse ${file}:`, e);
    return;
  }
  if (!Array.isArray(data)) {
    console.warn(`Expected array in ${file}, got ${typeof data}`);
    return;
  }
  const batch = db.batch();
  data.forEach(doc => {
    if (!doc.id) {
      console.warn(`Skipping doc without 'id' in ${file}`);
      return;
    }
    const ref = db.collection(collection).doc(doc.id);
    batch.set(ref, doc, { merge: true });
  });
  await batch.commit();
  console.log(`Imported ${data.length} docs to ${collection}`);
}

(async () => {
  for (const imp of imports) {
    await importCollection(imp);
  }
  console.log('All cleaned data imported!');
  process.exit(0);
})();
