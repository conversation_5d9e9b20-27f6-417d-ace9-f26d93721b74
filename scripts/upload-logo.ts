import { storage } from '@/lib/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import * as fs from 'fs';
import * as path from 'path';

async function uploadLogo() {
  try {
    const logoPath = path.join(process.cwd(), 'public', 'images', 'ai-logo1a.png');
    const file = await fs.promises.readFile(logoPath);
    
    // Create a reference to the file in Firebase Storage
    const storageRef = ref(storage, 'images/ai-logo1a.png');
    
    // Upload the file
    console.log('Uploading logo to Firebase Storage...');
    const snapshot = await uploadBytes(storageRef, file, {
      contentType: 'image/png',
      cacheControl: 'public, max-age=31536000', // Cache for 1 year
    });
    
    // Get the download URL
    const url = await getDownloadURL(snapshot.ref);
    console.log('Logo uploaded successfully!');
    console.log('Public URL:', url);
    
    return url;
  } catch (error) {
    console.error('Error uploading logo:', error);
    throw error;
  }
}

// Run the upload
uploadLogo()
  .then(() => process.exit(0))
  .catch(() => process.exit(1));
