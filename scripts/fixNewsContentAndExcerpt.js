// Usage: node scripts/fixNewsContentAndExcerpt.js
// Requires: Firebase Admin SDK configured for your project with serviceAccountKey.json
// This script migrates Firestore news docs so that:
// - If 'excerpt' contains the full article, it moves it to 'content' (as plain text)
// - Generates a true summary for 'excerpt' (first 150 chars, stripped of HTML)
// - Leaves 'content' alone if it is already valid (non-empty, not default, not just a summary)
// - Removes legacy 'body' fields if present

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

const serviceAccount = require(path.resolve(__dirname, '../service-account-key.json'));

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

const db = admin.firestore();

function stripHtml(html) {
  if (!html) return '';
  return html.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ').trim();
}

function isDefaultLexicalState(content) {
  return (
    !content ||
    content.trim() === '' ||
    content.trim() === '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}'
  );
}

async function fixNewsCollection() {
  const newsSnap = await db.collection('news').get();
  let updated = 0;
  for (const docSnap of newsSnap.docs) {
    const data = docSnap.data();
    let { excerpt, content, body } = data;
    let needsUpdate = false;

    const excerptText = (excerpt || '').trim();
    const contentText = stripHtml(content || '');

    // Determine if content is valid (not empty, not default, not just a summary, not too short)
    const isContentInvalid =
      isDefaultLexicalState(content) ||
      !contentText ||
      contentText.length < 30 ||
      contentText === excerptText ||
      contentText.length < excerptText.length + 10;

    // If content is invalid, but excerpt is long, move excerpt to content
    if (isContentInvalid && excerptText.length > 100) {
      content = excerpt;
      needsUpdate = true;
      console.log(`[${docSnap.id}] Moved excerpt to content`);
    }

    // Always generate a short summary for excerpt from content
    const summary = stripHtml(content || excerpt || '').slice(0, 150);
    if (
      !excerpt ||
      excerpt === content ||
      excerpt.length > 200 ||
      excerptText === contentText ||
      excerptText.length > 500 ||
      excerptText.length === contentText.length
    ) {
      excerpt = summary;
      needsUpdate = true;
      console.log(`[${docSnap.id}] Updated excerpt to summary`);
    }

    // Remove legacy body
    if (body) {
      delete data.body;
      needsUpdate = true;
      console.log(`[${docSnap.id}] Removed legacy body`);
    }

    if (needsUpdate) {
      await docSnap.ref.update({
        content,
        excerpt,
        body: admin.firestore.FieldValue.delete(),
      });
      updated++;
      console.log(`Updated news doc ${docSnap.id}`);
    }
  }
  console.log(`Done. Updated ${updated} news docs.`);
}

fixNewsCollection().catch(e => {
  console.error(e);
  process.exit(1);
});
