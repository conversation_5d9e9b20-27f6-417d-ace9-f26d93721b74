const { getStorage } = require("firebase-admin/storage");
const { getFirestore } = require("firebase-admin/firestore");
const { v4: uuidv4 } = require("uuid");
const path = require("path");
const admin = require("firebase-admin");

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  const serviceAccount = require("../serviceAccountKey.json"); // Path to your service account key
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: "kingdom2-9d1aa.appspot.com" // Your storage bucket
  });
}

const bucket = getStorage().bucket();
const firestore = getFirestore();

async function renameAndSyncImages() {
  try {
    console.log("Starting image renaming process...");
    
    // List all files in the Website Images folder
    const [files] = await bucket.getFiles({ prefix: "Website Images/" });
    console.log(`Found ${files.length} files to process`);

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Process each file
    for (const file of files) {
      const originalPath = file.name;
      
      try {
        // Skip directories and already processed files
        if (originalPath.endsWith("/")) {
          console.log(`Skipping directory: ${originalPath}`);
          skippedCount++;
          continue;
        }
        
        // Skip files that are already in UUID format
        const fileName = path.basename(originalPath);
        if (fileName.match(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}\..+$/i)) {
          console.log(`Skipping already processed file: ${originalPath}`);
          skippedCount++;
          continue;
        }

        // Generate new filename with UUID
        const ext = path.extname(originalPath);
        const uuid = uuidv4();
        const newPath = `Website Images/${uuid}${ext}`;

        console.log(`\nProcessing: ${originalPath}`);
        console.log(`  --> Renaming to: ${newPath}`);

        // Copy the file to the new location
        await file.copy(newPath);
        const newFile = bucket.file(newPath);
        
        // Make the new file public
        await newFile.makePublic();
        
        // Get the public URL
        const url = `https://storage.googleapis.com/${bucket.name}/${encodeURIComponent(newPath).replace(/%2F/g, '/')}`;
        
        // Save metadata to Firestore
        const docRef = firestore.collection("images").doc(uuid);
        await docRef.set({
          id: uuid,
          originalPath,
          newPath,
          downloadURL: url,
          originalName: path.basename(originalPath),
          fileExtension: ext.toLowerCase(),
          size: file.metadata.size,
          contentType: file.metadata.contentType,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`  ✓ Successfully processed ${originalPath}`);
        console.log(`  ✓ Public URL: ${url}`);
        
        // Delete the original file (uncomment to enable)
        // await file.delete();
        // console.log(`  ✓ Deleted original file: ${originalPath}`);
        
        processedCount++;
      } catch (error) {
        errorCount++;
        console.error(`  ✗ Error processing ${originalPath}:`, error.message);
      }
    }

    console.log("\n=== Process Complete ===");
    console.log(`Total files processed: ${processedCount}`);
    console.log(`Files skipped: ${skippedCount}`);
    console.log(`Errors encountered: ${errorCount}`);
    
  } catch (error) {
    console.error("Fatal error in renameAndSyncImages:", error);
    process.exit(1);
  }
}

// Run the function
renameAndSyncImages()
  .then(() => {
    console.log("Script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Script failed:", error);
    process.exit(1);
  });
