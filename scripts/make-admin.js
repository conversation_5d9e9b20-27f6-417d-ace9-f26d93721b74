const admin = require('firebase-admin');
const serviceAccount = require('../service-account-key.json');
require('dotenv').config();

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
});

const email = '<EMAIL>';

async function makeAdmin() {
  try {
    console.log(`Attempting to make ${email} a super admin...`);
    
    // Get the user by email
    const user = await admin.auth().getUserByEmail(email);
    
    console.log(`Found user: ${user.uid}`);
    
    // Set custom claims for super admin
    await admin.auth().setCustomUserClaims(user.uid, {
      admin: true,
      superAdmin: true
    });
    
    console.log(`✅ Successfully made ${email} a super admin!`);
    
    // Get the user again to verify the claims
    const updatedUser = await admin.auth().getUser(user.uid);
    console.log('Updated user claims:', updatedUser.customClaims);
    
  } catch (error) {
    console.error('❌ Error making user admin:', error);
    if (error.errorInfo) {
      console.error('Firebase error details:', error.errorInfo);
    }
  } finally {
    process.exit();
  }
}

makeAdmin();
