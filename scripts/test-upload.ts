// Test script to verify Firebase Storage uploads
import { initializeApp } from 'firebase/app';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import fs from 'fs/promises';
import path from 'path';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const storage = getStorage(app);

async function testUpload() {
  try {
    // Create a test file in memory
    const testContent = 'Test file content';
    const testFilePath = path.join(process.cwd(), 'test-upload.txt');
    await fs.writeFile(testFilePath, testContent);
    
    console.log('Created test file at:', testFilePath);
    
    // Read the test file
    const file = await fs.readFile(testFilePath);
    
    // Create a reference to the file in Firebase Storage
    const storageRef = ref(storage, `test-uploads/test-${Date.now()}.txt`);
    
    console.log('Uploading file to Firebase Storage...');
    
    // Upload the file
    const snapshot = await uploadBytes(storageRef, file);
    console.log('File uploaded successfully');
    
    // Get the download URL
    const url = await getDownloadURL(snapshot.ref);
    console.log('File available at:', url);
    
    // Clean up the test file
    await fs.unlink(testFilePath);
    console.log('Test file cleaned up');
    
    return { success: true, url };
  } catch (error) {
    console.error('Error during test upload:', error);
    return { success: false, error };
  }
}

// Run the test
testUpload()
  .then(({ success, url, error }) => {
    if (success) {
      console.log('✅ Test upload successful!');
      console.log('📎 URL:', url);
      process.exit(0);
    } else {
      console.error('❌ Test upload failed:', error);
      process.exit(1);
    }
  });
