import admin from 'firebase-admin';
import * as dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Initialize Firebase Admin
const serviceAccount = require('../service-account-key.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
});

const email = '<EMAIL>';

async function makeAdmin() {
  try {
    // Get the user by email
    const user = await admin.auth().getUserByEmail(email);
    
    // Set custom claims for super admin
    await admin.auth().setCustomUserClaims(user.uid, {
      admin: true,
      superAdmin: true
    });
    
    console.log(`✅ Successfully made ${email} a super admin!`);
    
    // Get the user again to verify the claims
    const updatedUser = await admin.auth().getUser(user.uid);
    console.log('Updated user claims:', updatedUser.customClaims);
    
  } catch (error) {
    console.error('❌ Error making user admin:', error);
  } finally {
    process.exit();
  }
}

makeAdmin();
