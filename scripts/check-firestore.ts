import { initializeApp, cert, getApps } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';

// Load environment variables
require('dotenv').config({ path: '../.env.local' });

// Initialize Firebase Admin
const serviceAccount = process.env.FIREBASE_SERVICE_ACCOUNT_KEY;

if (!serviceAccount) {
  console.error('FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set');
  process.exit(1);
}

// Parse the service account key
let serviceAccountJson;
try {
  serviceAccountJson = JSON.parse(Buffer.from(serviceAccount, 'base64').toString('utf-8'));
} catch (error) {
  console.error('Failed to parse FIREBASE_SERVICE_ACCOUNT_KEY:', error);
  process.exit(1);
}

// Initialize Firebase Admin if not already initialized
if (getApps().length === 0) {
  initializeApp({
    credential: cert(serviceAccountJson),
    databaseURL: `https://${serviceAccountJson.project_id}.firebaseio.com`
  });
}

const db = getFirestore();

async function checkFirestoreData() {
  try {
    console.log('Checking Firestore data...');
    
    // List all collections
    const collections = await db.listCollections();
    console.log('\nCollections in Firestore:');
    for (const collection of collections) {
      console.log(`- ${collection.id}`);
      
      // Get the first few documents in each collection
      const snapshot = await collection.limit(3).get();
      console.log(`  Documents (first 3):`);
      snapshot.forEach(doc => {
        console.log(`  - ${doc.id}:`, JSON.stringify(doc.data(), null, 2));
      });
    }
    
    // Check if adminUser collection exists and has data
    const adminSnapshot = await db.collection('adminUser').limit(1).get();
    if (adminSnapshot.empty) {
      console.log('\nNo admin users found in the adminUser collection');
    } else {
      console.log('\nAdmin users (first 1):');
      adminSnapshot.forEach(doc => {
        console.log(JSON.stringify(doc.data(), null, 2));
      });
    }
    
  } catch (error) {
    console.error('Error checking Firestore data:', error);
  } finally {
    process.exit(0);
  }
}

// Run the check
checkFirestoreData();
