// Usage: node scripts/exportFirebaseStorageFiles.js
// Lists all Firebase Storage files and downloads them to ./firebase-storage
// Requires service-account-key.json in project root

const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });
const admin = require('firebase-admin');
const fetch = require('node-fetch');

const serviceAccount = require('../service-account-key.json');

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET
  });
}

const bucket = admin.storage().bucket();

async function listAndDownloadFiles() {
  const [files] = await bucket.getFiles();
  if (!fs.existsSync(path.join(__dirname, '../firebase-storage'))){
    fs.mkdirSync(path.join(__dirname, '../firebase-storage'));
  }
  for (const file of files) {
    // Skip directory placeholders (Firebase Storage may list folders as objects ending with '/')
    if (file.name.endsWith('/')) {
      continue;
    }
    const dest = path.join(__dirname, '../firebase-storage', file.name);
    const dir = path.dirname(dest);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    console.log(`Downloading: ${file.name}`);
    await file.download({ destination: dest });
  }
  console.log('All files downloaded!');
}

listAndDownloadFiles().catch(console.error);
