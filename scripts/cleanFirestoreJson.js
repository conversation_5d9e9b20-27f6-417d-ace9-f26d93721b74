// Usage: node scripts/cleanFirestoreJson.js
// Cleans all exported Firestore JSON collections in the project root.
// Produces new files named <collection>.cleaned.json (originals are never overwritten).

const fs = require('fs');
const path = require('path');

// List your exported JSON files here (add more as needed)
const collections = [
  'news.json',
  'event.json',
  'gallery.json',
  'strategicPartner.json',
  'users.json',
  'adminUser.json',
  'category.json',
  'newsletter.json',
  'page.json',
  'pageView.json',
  'post.json',
  'rsvp.json',
  'siteSettings.json',
  'storeCategory.json',
  'websiteAssets.json',
  'websiteImages.json',
];

function isLegacyField(key, value) {
  // Remove all fields starting with _ (Sanity system fields)
  if (key.startsWith('_')) return true;
  // Remove legacy 'body' if not a string (block array/object/null)
  if (key === 'body' && (value === null || typeof value !== 'string')) return true;
  // Remove Sanity ref objects (with _type/_ref)
  if (value && typeof value === 'object' && ('_type' in value || '_ref' in value)) return true;
  return false;
}

function cleanObject(obj) {
  if (Array.isArray(obj)) {
    return obj.map(cleanObject).filter(Boolean);
  } else if (obj && typeof obj === 'object') {
    const cleaned = {};
    for (const [key, value] of Object.entries(obj)) {
      if (isLegacyField(key, value)) continue;
      if (typeof value === 'object' && value !== null) {
        cleaned[key] = cleanObject(value);
      } else {
        cleaned[key] = value;
      }
    }
    return cleaned;
  }
  return obj;
}

collections.forEach(filename => {
  const filePath = path.join(__dirname, '../', filename);
  if (!fs.existsSync(filePath)) {
    console.warn(`File not found: ${filename}`);
    return;
  }
  const raw = fs.readFileSync(filePath, 'utf-8');
  let data;
  try {
    data = JSON.parse(raw);
  } catch (e) {
    console.error(`Failed to parse ${filename}:`, e);
    return;
  }
  const cleaned = cleanObject(data);
  const outPath = path.join(__dirname, '../', filename.replace('.json', '.cleaned.json'));
  fs.writeFileSync(outPath, JSON.stringify(cleaned, null, 2));
  console.log(`Cleaned: ${filename} → ${path.basename(outPath)}`);
});
