// Usage: node scripts/exportFirestoreAllCollections.js
// Exports all Firestore collections as JSON files in the project root

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Use your existing firebaseAdmin.ts config
const serviceAccount = require('../service-account-key.json');

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

const db = admin.firestore();

async function exportCollection(collectionName) {
  const snapshot = await db.collection(collectionName).get();
  const data = [];
  snapshot.forEach((doc) => {
    data.push({ id: doc.id, ...doc.data() });
  });
  fs.writeFileSync(
    path.join(__dirname, `../${collectionName}.json`),
    JSON.stringify(data, null, 2)
  );
  console.log(`Exported ${collectionName} (${data.length} docs)`);
}

async function exportAllCollections() {
  const collections = await db.listCollections();
  for (const collection of collections) {
    await exportCollection(collection.id);
  }
  console.log('All collections exported!');
}

exportAllCollections();
