import { NextApiRequest, NextApiResponse } from 'next';
import { setAdminPrivileges } from '@/lib/admin-utils';
import { adminAuth } from '@/lib/firebaseAdmin';

// This is a protected API route that should only be accessible in development
// or by users who are already admins
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { email, isAdmin = true } = req.body;

  if (!email) {
    return res.status(400).json({ error: 'Email is required' });
  }

  try {
    // Verify the requesting user is an admin
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await adminAuth.verifyIdToken(idToken);
    
    // Check if the user is already an admin
    const isUserAdmin = decodedToken.admin || decodedToken.superAdmin;
    if (!isUserAdmin && process.env.NODE_ENV === 'production') {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Set admin privileges
    await setAdminPrivileges(email, isAdmin);
    
    // Get the updated user to return
    const user = await adminAuth.getUserByEmail(email);
    
    return res.status(200).json({ 
      success: true,
      user: {
        uid: user.uid,
        email: user.email,
        isAdmin: isAdmin,
        customClaims: {
          ...user.customClaims,
          admin: isAdmin,
          superAdmin: isAdmin
        }
      }
    });
  } catch (error) {
    console.error('Error in make-admin API:', error);
    return res.status(500).json({ 
      error: 'Failed to update admin status',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
