const path = require('path');

module.exports = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Exclude Firebase Functions and Storage from the build
  typescript: {
    // Exclude Firebase Functions from TypeScript compilation
    ignoreBuildErrors: true,
  },
  // Exclude specific directories from being processed by webpack
  transpilePackages: [],
  // Move outputFileTracingExcludes to root level (removed from experimental)
  outputFileTracingExcludes: {
    '*': [
      './functions/**/*',
      './firebase-storage/**/*',
      './scripts/**/*',
      './**/*.test.*',
      './**/*.spec.*',
    ],
  },
  // Exclude source maps from production build
  productionBrowserSourceMaps: false,
  // Image optimization configuration
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'firebasestorage.googleapis.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'storage.googleapis.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // Webpack configuration
  webpack: (config, { isServer }) => {
    // Disable source maps in production for client-side code
    if (!isServer) {
      config.devtool = false;
    }
    
    // Set up path aliases
    config.resolve.alias['@'] = path.resolve(__dirname);
    
    // Important: return the modified config
    return config;
  },
  // Enable React strict mode
  reactStrictMode: true,
};
