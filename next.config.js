const path = require('path');

module.exports = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // Webpack configuration to handle Firebase modules properly
  webpack: (config, { isServer }) => {
    // Handle Firebase modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
      };
    }

    // Optimize Firebase bundle
    config.resolve.alias = {
      ...config.resolve.alias,
      '@firebase/app': path.resolve(__dirname, 'node_modules/@firebase/app'),
      '@firebase/firestore': path.resolve(__dirname, 'node_modules/@firebase/firestore'),
      '@firebase/auth': path.resolve(__dirname, 'node_modules/@firebase/auth'),
      '@firebase/storage': path.resolve(__dirname, 'node_modules/@firebase/storage'),
    };

    return config;
  },
  // Exclude specific directories from being processed
  outputFileTracingExcludes: {
    '*': [
      './functions/**/*',
      './firebase-storage/**/*',
      './scripts/**/*',
      './**/*.test.*',
      './**/*.spec.*',
    ],
  },
  // Exclude source maps from production build
  productionBrowserSourceMaps: false,
  // Image optimization configuration
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'firebasestorage.googleapis.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'storage.googleapis.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // Webpack configuration
  webpack: (config, { isServer }) => {
    // Disable source maps in production for client-side code
    if (!isServer) {
      config.devtool = false;
    }
    
    // Set up path aliases
    config.resolve.alias['@'] = path.resolve(__dirname);
    
    // Important: return the modified config
    return config;
  },
  // Enable React strict mode
  reactStrictMode: true,
};
