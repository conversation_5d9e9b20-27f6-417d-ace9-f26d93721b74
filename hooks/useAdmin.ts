import { useState, useEffect } from 'react';
import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { getIdTokenResult } from 'firebase/auth';

export const useAdmin = () => {
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isSuperAdmin, setIsSuperAdmin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const auth = getAuth();
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      try {
        if (user) {
          // Get the ID token result which contains the custom claims
          const idTokenResult = await user.getIdTokenResult();
          const isUserAdmin = idTokenResult.claims.admin === true || idTokenResult.claims.superAdmin === true;
          const isUserSuperAdmin = idTokenResult.claims.superAdmin === true;
          
          setIsAdmin(isUserAdmin);
          setIsSuperAdmin(isUserSuperAdmin);
        } else {
          setIsAdmin(false);
          setIsSuperAdmin(false);
        }
      } catch (err) {
        console.error('Error checking admin status:', err);
        setError(err instanceof Error ? err : new Error('Failed to check admin status'));
      } finally {
        setLoading(false);
      }
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  // Function to refresh the admin status
  const refreshAdminStatus = async () => {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (user) {
        // Force token refresh to get the latest claims
        await user.getIdToken(true);
        const idTokenResult = await user.getIdTokenResult();
        const isUserAdmin = idTokenResult.claims.admin === true || idTokenResult.claims.superAdmin === true;
        const isUserSuperAdmin = idTokenResult.claims.superAdmin === true;
        
        setIsAdmin(isUserAdmin);
        setIsSuperAdmin(isUserSuperAdmin);
      } else {
        setIsAdmin(false);
        setIsSuperAdmin(false);
      }
    } catch (err) {
      console.error('Error refreshing admin status:', err);
      setError(err instanceof Error ? err : new Error('Failed to refresh admin status'));
    }
  };

  return { isAdmin, isSuperAdmin, loading, error, refreshAdminStatus };
};

export default useAdmin;
