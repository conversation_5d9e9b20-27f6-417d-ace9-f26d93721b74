'use client';

import { useState } from 'react';
import { toast } from '@/hooks/use-toast';
import { syncStorageWithFirestore } from '@/lib/sync-utils';

interface SyncResult {
  success: boolean;
  message: string;
  syncedItems?: any[];
}

interface ProcessItemFn {
  (item: any, url: string, metadata: any): Promise<Record<string, any>> | Record<string, any>;
}

interface TransformExistingFn {
  (existingDoc: any, itemRef: any, metadata: any): Promise<Record<string, any> | null> | Record<string, any> | null;
}

interface UseSyncDataProps {
  storagePath: string;
  collectionName: string;
  processItem: ProcessItemFn;
  transformExisting?: TransformExistingFn;
  onSyncComplete?: (result: SyncResult) => void;
}

export function useSyncData({
  storagePath,
  collectionName,
  processItem,
  transformExisting,
  onSyncComplete
}: UseSyncDataProps) {
  const [isSyncing, setIsSyncing] = useState(false);

  const handleSync = async () => {
    setIsSyncing(true);
    try {
      toast({
        title: 'Syncing...',
        description: `Synchronizing ${collectionName} with storage`,
      });

      const result = await syncStorageWithFirestore(storagePath, collectionName, {
        processItem,
        transformExisting,
      });
      
      // Handle the result based on its structure
      let syncResult: SyncResult;
      if (Array.isArray(result)) {
        syncResult = { success: true, message: `Synced ${result.length} items`, syncedItems: result };
      } else if (result && typeof result === 'object' && 'success' in result) {
        syncResult = result as SyncResult;
      } else {
        syncResult = { success: false, message: 'Unknown sync result format' };
      }
      
      if (syncResult.success) {
        toast({
          title: 'Sync Complete',
          description: syncResult.message || `Successfully synced ${collectionName}`,
          variant: 'default',
        });
        
        if (onSyncComplete) {
          onSyncComplete(syncResult);
        }
      } else {
        throw new Error(syncResult.message || 'Sync failed');
      }
      
      return syncResult;
    } catch (error) {
      console.error(`Error syncing ${collectionName}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred during sync';
      
      toast({
        title: 'Sync Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      
      return { success: false, message: errorMessage };
    } finally {
      setIsSyncing(false);
    }
  };

  return { isSyncing, handleSync };
}
