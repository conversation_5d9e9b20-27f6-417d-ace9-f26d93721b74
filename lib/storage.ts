// Base URL for images in the public folder
export const LOGO_URL = '/images/ai-logo1a.png';

/**
 * Helper function to get the full public URL for a file in the public folder
 * @param path The path to the file in the public folder (e.g., 'images/filename.jpg')
 * @returns Full public URL to the file
 */
export const getImageUrl = (path: string) => {
  // For non-logo images, use the path as is (assuming it's already a full URL or relative path)
  return path.startsWith('http') ? path : `/${path.replace(/^\/+/, '')}`;
};

/**
 * Generates a Firebase Storage URL for the given file path
 * @param path Path to the file in Firebase Storage (e.g., 'Website Images/Royal Coronation Ceremony.png')
 * @returns Full public URL to access the file
 */
export function getStorageUrl(path: string): string {
  // URL encode the path components
  const encodedPath = path.split('/')
    .map(segment => encodeURIComponent(segment))
    .join('/');
    
  return `https://firebasestorage.googleapis.com/v0/b/kingdom2-9d1aa.appspot.com/o/${encodedPath}?alt=media`;
}

/**
 * Gets the URL for the Freedom & Justice image
 */
export const FREEDOM_JUSTICE_IMAGE = getStorageUrl('Website Images/Royal Coronation Ceremony.png');
