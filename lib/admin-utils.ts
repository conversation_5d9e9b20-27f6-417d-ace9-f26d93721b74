import { adminAuth } from './firebaseAdmin';

export const setAdminPrivileges = async (email: string, isAdmin: boolean = true) => {
  try {
    // Get user by email
    const user = await adminAuth.getUserByEmail(email);
    
    // Set custom admin claim
    await adminAuth.setCustomUserClaims(user.uid, { 
      admin: isAdmin,
      superAdmin: isAdmin // Adding superAdmin for extra clarity
    });
    
    console.log(`Successfully ${isAdmin ? 'granted' : 'revoked'} admin privileges for ${email}`);
    return true;
  } catch (error) {
    console.error('Error setting admin privileges:', error);
    throw error;
  }
};

export const isAdmin = async (uid: string) => {
  try {
    const user = await adminAuth.getUser(uid);
    return user.customClaims?.admin === true || user.customClaims?.superAdmin === true;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
};

export const isSuperAdmin = async (uid: string) => {
  try {
    const user = await adminAuth.getUser(uid);
    return user.customClaims?.superAdmin === true;
  } catch (error) {
    console.error('Error checking super admin status:', error);
    return false;
  }
};
