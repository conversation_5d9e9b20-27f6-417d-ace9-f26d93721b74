import * as admin from 'firebase-admin';

// Check if Firebase Admin is already initialized
if (!admin.apps.length) {
  // If running in a production environment, use the service account credentials
  if (process.env.FIREBASE_ADMIN_PRIVATE_KEY) {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
        clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
        // The private key needs to be properly formatted as it's stored with escaped newlines in env
        privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
      databaseURL: process.env.FIREBASE_ADMIN_DATABASE_URL,
    });
  } else {
    // For local development, you can use a service account key file
    // Make sure to add this file to .gitignore to avoid committing sensitive credentials
    try {
      const serviceAccount = require('@/service-account-key.json');
      
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        databaseURL: process.env.FIREBASE_ADMIN_DATABASE_URL,
      });
    } catch (error) {
      console.error('Failed to initialize Firebase Admin:', error);
      // Initialize with default app config if service account is not available
      admin.initializeApp();
    }
  }
}

// Export the admin instances
export const adminFirestore = admin.firestore();
export const adminAuth = admin.auth();
export const adminStorage = admin.storage();

export default admin;
