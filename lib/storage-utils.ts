import { ref, listAll, getDownloadURL, getMetadata, StorageReference } from 'firebase/storage';
import { storage } from './firebase';

export interface StorageImage {
  name: string;
  url: string;
  fullPath: string;
  timeCreated: string;
  updated: string;
}

export async function listImagesInFolder(folderPath: string = 'Website Images'): Promise<StorageImage[]> {
  try {
    const folderRef = ref(storage, folderPath);
    const result = await listAll(folderRef);
    
    const images: StorageImage[] = [];
    
    for (const itemRef of result.items) {
      try {
        const [url, metadata] = await Promise.all([
          getDownloadURL(itemRef),
          getMetadata(itemRef)
        ]);
        
        images.push({
          name: itemRef.name,
          url,
          fullPath: itemRef.fullPath,
          timeCreated: metadata.timeCreated || new Date().toISOString(),
          updated: metadata.updated || new Date().toISOString()
        });
      } catch (error) {
        console.error(`Error processing ${itemRef.fullPath}:`, error);
      }
    }
    
    return images;
  } catch (error) {
    console.error('Error listing images:', error);
    throw error;
  }
}

// Helper function to get the URL for a specific image
export function getImageUrl(imagePath: string): string {
  const encodedPath = encodeURIComponent(imagePath).replace(/%2F/g, '/');
  const storageBucket = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET;
  if (!storageBucket) {
    throw new Error('Firebase Storage bucket is not configured');
  }
  return `https://firebasestorage.googleapis.com/v0/b/${storageBucket}/o/${encodedPath}?alt=media`;
}
