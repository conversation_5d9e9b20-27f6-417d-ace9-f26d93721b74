import * as admin from 'firebase-admin';

// Check if Firebase Admin is already initialized
if (!admin.apps.length) {
  try {
    // Try to use the service account key from environment variables
    let serviceAccount = null;
    const rawKey = process.env.FIREBASE_SERVICE_ACCOUNT_KEY;

    if (rawKey) {
      try {
        // Check if it's already JSON or base64 encoded
        if (rawKey.trim().startsWith('{')) {
          serviceAccount = JSON.parse(rawKey);
        } else {
          // Decode from base64
          const decoded = Buffer.from(rawKey, 'base64').toString('utf8');
          serviceAccount = JSON.parse(decoded);
        }
      } catch (err) {
        console.error('Failed to parse FIREBASE_SERVICE_ACCOUNT_KEY:', err);
      }
    }

    if (serviceAccount && serviceAccount.project_id) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
      });

      if (process.env.NODE_ENV === 'development') {
        console.log('Firebase Admin SDK initialized successfully');
      }
    } else {
      console.error('FIREBASE_SERVICE_ACCOUNT_KEY is missing or invalid. Firebase Admin SDK will not be initialized.');
    }
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
    // Don't throw here to allow the app to continue with limited functionality
  }
}

// Export the admin instances with error handling
let firestore: admin.firestore.Firestore | null = null;
let auth: admin.auth.Auth | null = null;
let storage: admin.storage.Storage | null = null;

try {
  if (admin.apps.length > 0) {
    firestore = admin.firestore();
    auth = admin.auth();
    storage = admin.storage();
  }
} catch (error) {
  console.error('Error initializing Firebase Admin services:', error);
}

export { firestore, auth, storage };
export default admin;
