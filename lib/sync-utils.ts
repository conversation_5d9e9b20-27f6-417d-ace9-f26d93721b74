import { ref, listAll, getDownloadURL, getMetadata, StorageReference } from 'firebase/storage';
import { collection, doc, setDoc, updateDoc, serverTimestamp, DocumentData, getDocs } from 'firebase/firestore';
import { db, storage } from './firebase';

type ProcessItemFn = (item: StorageReference, url: string, metadata: any) => Promise<DocumentData> | DocumentData;
type TransformExistingFn = (existingDoc: DocumentData, itemRef: StorageReference, metadata: any) => Promise<DocumentData | null> | DocumentData | null;

interface SyncOptions {
  processItem: ProcessItemFn;
  transformExisting?: TransformExistingFn;
}

export async function syncStorageWithFirestore(
  storagePath: string,
  collectionName: string,
  options: ProcessItemFn | SyncOptions
) {
  console.log(`[syncStorageWithFirestore] Starting sync for path: ${storagePath}, collection: ${collectionName}`);
  
  // Handle both simple function and options object
  const processItem = typeof options === 'function' ? options : options.processItem;
  const transformExisting = typeof options === 'function' ? undefined : options.transformExisting;
  
  try {
    // Get all existing items from Firestore
    console.log('[syncStorageWithFirestore] Fetching existing Firestore documents...');
    const existingItems = new Map<string, { id: string; [key: string]: any }>();
    const collectionRef = collection(db, collectionName);
    const querySnapshot = await getDocs(collectionRef);
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      if (data) {
        // Use storagePath if available, otherwise fall back to imageUrl
        const key = data.storagePath || data.imageUrl;
        if (key) {
          existingItems.set(key, { id: doc.id, ...data });
        }
      }
    });
    console.log(`[syncStorageWithFirestore] Found ${existingItems.size} existing items in Firestore`);

    // List all files in storage
    console.log(`[syncStorageWithFirestore] Listing files in storage path: ${storagePath}`);
    const storageRef = ref(storage, storagePath);
    const storageResult = await listAll(storageRef);
    console.log(`[syncStorageWithFirestore] Found ${storageResult.items.length} items in storage`);
    
    const batch: Promise<void>[] = [];
    const processedItems: any[] = [];

    // Process each file
    console.log('[syncStorageWithFirestore] Starting to process items...');
    for (const itemRef of storageResult.items) {
      console.log(`[syncStorageWithFirestore] Processing item: ${itemRef.fullPath}`);
      try {
        const url = await getDownloadURL(itemRef);
        const metadata = await getMetadata(itemRef);
        const existingItem = Array.from(existingItems.values()).find(
          item => item.storagePath === itemRef.fullPath || item.imageUrl === url
        );
        
        if (!existingItem) {
          console.log(`[syncStorageWithFirestore] New item found: ${itemRef.fullPath}`);
          try {
            const newItem = await Promise.resolve(processItem(itemRef, url, metadata));
            const newDoc = doc(collection(db, collectionName));
            
            console.log(`[syncStorageWithFirestore] Creating new document for: ${itemRef.fullPath}`);
            batch.push(setDoc(newDoc, {
              ...newItem,
              id: newDoc.id,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp()
            }));
            
            processedItems.push({ id: newDoc.id, ...newItem });
            console.log(`[syncStorageWithFirestore] Successfully queued document creation for: ${itemRef.fullPath}`);
          } catch (processError) {
            console.error(`[syncStorageWithFirestore] Error processing new item ${itemRef.fullPath}:`, processError);
          }
        } else if (transformExisting) {
          // Update existing document if transform function is provided
          const updates = await Promise.resolve(transformExisting(existingItem, itemRef, metadata));
          if (updates) {
            const docRef = doc(db, collectionName, existingItem.id);
            batch.push(updateDoc(docRef, {
              ...updates,
              updatedAt: serverTimestamp()
            }));
          }
        }
      } catch (error) {
        console.error(`Error processing ${itemRef.name}:`, error);
      }
    }

    console.log(`[syncStorageWithFirestore] Executing ${batch.length} batch operations...`);
    await Promise.all(batch);
    console.log(`[syncStorageWithFirestore] Sync completed. Processed ${processedItems.length} items.`);
    return processedItems;
  } catch (error) {
    console.error('[syncStorageWithFirestore] Error syncing storage with Firestore:', error);
    throw error;
  }
}
