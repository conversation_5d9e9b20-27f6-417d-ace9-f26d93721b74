/**
 * Represents a user in the system with authentication and profile information.
 */
export interface User {
  /** Unique identifier for the user */
  uid: string;
  
  /** User's email address */
  email: string | null;
  
  /** User's display name */
  displayName: string | null;
  
  /** URL to the user's profile photo */
  photoURL: string | null;
  
  /** Whether the user's email has been verified */
  emailVerified: boolean;
  
  /** Whether the user account is disabled */
  disabled: boolean;
  
  /** When the user was created (ISO string) */
  createdAt?: string;
  
  /** When the user was last updated (ISO string) */
  updatedAt?: string;
  
  /** When the user last signed in (ISO string) */
  lastSignInTime?: string;
  
  /** When the user was created (ISO string) - alias for createdAt */
  creationTime?: string;
}

/**
 * Represents an admin user with role-based access control
 */
export interface AdminUser extends User {
  /** User's role in the admin system */
  role: 'editor' | 'admin' | 'super_admin';
  
  /** Additional metadata for the user */
  metadata?: {
    /** When the user last signed in (ISO string from Firebase Auth) */
    lastSignInTime?: string;
    
    /** Any custom claims associated with the user */
    customClaims?: Record<string, any>;
  };
}

/**
 * Type for user role options
 */
export type UserRole = 'editor' | 'admin' | 'super_admin';

/**
 * Type for user status options
 */
export type UserStatus = 'active' | 'inactive';

/**
 * Type for user creation data
 */
export interface CreateUserData {
  email: string;
  password: string;
  displayName?: string;
  photoURL?: string;
  role?: UserRole;
  disabled?: boolean;
}

/**
 * Type for user update data
 */
export interface UpdateUserData {
  displayName?: string;
  photoURL?: string | null;
  email?: string;
  disabled?: boolean;
  role?: UserRole;
  updatedAt?: string;
}
