/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        royalGold: {
          50: "#FFF9E6",
          100: "#FEF3CC",
          200: "#FDE699",
          300: "#FBD966",
          400: "#F9CC33",
          500: "#D4AF37",
          600: "#B38F2D",
          700: "#8F7023",
          800: "#6B5119",
          900: "#47320F",
        },
        royalBlue: {
          50: "#E6F0FF",
          100: "#CCE0FF",
          200: "#99C2FF",
          300: "#66A3FF",
          400: "#3385FF",
          500: "#0066FF",
          600: "#0052CC",
          700: "#003D99",
          800: "#002966",
          900: "#001433",
        },
        ivory: {
          50: "#FFFFFF",
          100: "#FEFEFD",
          200: "#FCFBF4",
          300: "#F9F8E7",
          400: "#F7F6E2",
          500: "#F5F5DC",
          600: "#E6E3B8",
          700: "#D7D194",
          800: "#C8BF70",
          900: "#B9AD4C",
        },
        forestGreen: {
          50: "#E6F5EB",
          100: "#CCEBD7",
          200: "#99D7AF",
          300: "#66C387",
          400: "#33AF5F",
          500: "#014421",
          600: "#01361A",
          700: "#012814",
          800: "#001B0D",
          900: "#000D07",
        },
      },
      fontFamily: {
        sans: ["Montserrat", "sans-serif"],
        serif: ["Garamond", "serif"],
      },
    },
  },
  plugins: [],
};
