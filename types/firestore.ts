// TypeScript interfaces for Firestore collections, auto-generated from schema summary

// --- News ---
export interface News {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  publishedAt: string;
  category?: string;
  categoryName?: string;
  mainImage?: string;
  status?: string;
  readTime?: string;
  // Legacy/optional fields
  body?: string; // Should be removed, but may exist as string
  featured?: boolean;
}

// --- Gallery ---
export interface Gallery {
  id: string;
  title: string;
  description: string;
  image?: string;
  images?: string[];
  category?: string;
  featured?: boolean;
  tags?: string[];
  order?: number;
  publishedAt?: string;
  backgroundStyle?: string;
  displayStyle?: string;
}

// --- Event ---
export interface Event {
  id: string;
  title: string;
  date: string;
  endDate?: string;
  description: string;
  location: string;
  imageUrl?: string;
  imageAlt?: string;
  eventType?: string;
  isCountdownTarget?: boolean;
  isHighlighted?: boolean;
  showRsvp?: boolean;
  order?: number;
  seo?: Record<string, any>; // Consider stricter typing if known
}

// --- Category ---
export interface Category {
  id: string;
  title: string;
  description?: string;
  color?: string;
  order?: number;
}

// --- User ---
export interface User {
  id: string;
  name: string;
  email: string;
  role?: string;
  isActive?: boolean;
  lastLogin?: string;
  permissions?: Record<string, any>;
  isProtected?: boolean;
}

// --- Page ---
export interface Page {
  id: string;
  title: string;
  description?: string;
  slug?: string;
  seoTitle?: string;
  seoDescription?: string;
  navMenu?: boolean;
  navOrder?: number;
  pageBuilder?: any[];
  accessLevel?: string;
}

// --- Newsletter ---
export interface Newsletter {
  id: string;
  email: string;
  name?: string;
  submittedAt?: string;
}

// --- Contact ---
export interface Contact {
  id: string;
  name: string;
  email: string;
  subject?: string;
  message: string;
  submittedAt?: string;
  phone?: string;
}
