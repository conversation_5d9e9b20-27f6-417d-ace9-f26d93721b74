export interface RSVP {
  id: string;
  _createdAt: string;
  _updatedAt: string;
  _type: 'rsvp';
  attendanceType: 'online' | 'physical';
  country: string;
  email: string;
  events: string[];
  firstName: string;
  lastName: string;
  notes: string;
  phone: string;
  reminderPreference: ('sms' | 'email')[];
  submittedAt: string;
  [key: string]: any; // For any additional fields
}

export interface CSVRow {
  'First Name': string;
  'Last Name': string;
  'Email': string;
  'Phone': string;
  'Country': string;
  'Attendance Type': string;
  'Events': string;
  'Reminder Preference': string;
  'Notes': string;
  'Submitted At': string;
}
