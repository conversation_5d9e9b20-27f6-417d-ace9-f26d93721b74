export interface NewsImage {
  id: string;
  url: string;
  caption?: string;
  isFeatured: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface News {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage?: string; // Main/featured image URL
  featuredImagePath?: string; // Path to featured image in storage
  images?: string[]; // Array of image URLs
  imagesPaths?: string[]; // Array of paths to images in storage
  publishedAt: string;
  status: 'draft' | 'published';
  featured: boolean;
  authorId: string;
  authorName?: string;
  categories?: string[];
  tags?: string[];
  viewCount?: number;
  createdAt: string;
  updatedAt: string;
}
