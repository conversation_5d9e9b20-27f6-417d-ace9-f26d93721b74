export interface Event {
  id: string;
  title: string;
  date: any; // Firestore Timestamp or ISO string
  endDate?: any;
  description: string;
  location: string;
  imageUrl?: string;
  imageAlt?: string;
  eventType?: string;
  isCountdownTarget?: boolean;
  isHighlighted?: boolean;
  showRsvp?: boolean;
  order?: number;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    [key: string]: any;
  };
  [key: string]: any;
}
