export interface GalleryItem {
  id: string;
  title: string;
  description?: string;
  imageUrl: string;
  storagePath: string; // Path in Firebase Storage
  thumbnailUrl?: string;
  thumbnailStoragePath?: string; // Path to thumbnail in Firebase Storage
  images?: Array<{
    url: string;
    storagePath: string;
    thumbnailUrl?: string;
    thumbnailStoragePath?: string;
    name: string;
    size: number;
    type: string;
  }>;
  featured: boolean;
  createdAt: string | Date;
  updatedAt: string | Date;
  order?: number;
  tags?: string[];
  status: 'published' | 'draft';
}
